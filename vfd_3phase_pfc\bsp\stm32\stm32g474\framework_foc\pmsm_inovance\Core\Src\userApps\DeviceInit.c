#include "DeviceInit.h"
#include "MotorDefine.h"
#include "MotorInclude.h"
//#include "bsp_FDCAN.h"
#if 1
volatile uint16_t gAdcResult[ADC_12_CHNNS] = {0};
#else
uint32_t gAdcResult[ADC_12_CHNNS] = {0};
#endif
uint16_t gAdcRes_Iu;
uint16_t gAdcRes_Iv;
uint16_t gAdcRes_Iw;
uint16_t gAdcRes_Udc;

uint16_t gAdcRes_IgbtTemp ;
uint16_t gAdcRes_Vr;
uint16_t gAdcRes_Vs;
uint16_t gAdcRes_Aux;

//----------------------------------------------------------
s16 CC1, CC2, CC3, CC4, CC5, CC6, CC7, CC8;
//----------------------------------------------------------



//----------------------------------------------------------

void InitPeripherals(void)
{
//  HAL_GPIO_WritePin(GPIOF, GPIO_PIN_15, GPIO_PIN_RESET);

    //-------------------------------------------------------------------
    //Correct the ADC values
    if (HAL_ADCEx_Calibration_Start(&hadc1, ADC_SINGLE_ENDED) != HAL_OK)
    {
        Error_Handler();
    }
//    if (HAL_ADCEx_Calibration_Start(&hadc2, ADC_SINGLE_ENDED) != HAL_OK)
//    {
//        Error_Handler();
//    }

    //-------------------------------------------------------------------
    //čľˇĺ?é?çčŽžç˝Žä¸? 5kHz
    __HAL_TIM_SET_AUTORELOAD(&htim1, C_INIT_PRD - 1);
    (&htim1)->Instance->BDTR |= (0x000000CF); // 2.21us BDTR->DTG[7:0]= 110 01111. (32+15)*8*5.88ns = 2.21us
    //ĺĺ?ĺ çŠşćŻčŽžç˝Žä¸? 50%
    __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_1, C_INIT_PRD / 2 - 1);
    __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_2, C_INIT_PRD / 2 - 1);
    __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_3, C_INIT_PRD / 2 - 1);

}
extern uint32_t gTim1_break_times;
extern BEFORE_RUN_PHASE_LOSE_STRUCT gBforeRunPhaseLose;
__IO uint8_t gEnablePwmFlag = 0;
void DisableDriveFs(void)
{
    if(gBforeRunPhaseLose.UWPhaseLoseFlag &&
        (gBforeRunPhaseLose.Counter < 6))
    {
        
    }
    gTim1_break_times = 0;
    gEnablePwmFlag = 0;
    SynInitPosDetSetPwm(7);
    //rt_pin_write(FS_DR_PIN,0);
}

void EnableDriveFs(void)
{
    gEnablePwmFlag = 1;
    //rt_pin_write(FS_DR_PIN,1);
}

void RunTask(void)
{

//    HAL_TIM_PWM_Start(&htim1, TIM_CHANNEL_1);
//    HAL_TIMEx_PWMN_Start(&htim1, TIM_CHANNEL_1);

//    HAL_TIM_PWM_Start(&htim1, TIM_CHANNEL_2);
//    HAL_TIMEx_PWMN_Start(&htim1, TIM_CHANNEL_2);

//    HAL_TIM_PWM_Start(&htim1, TIM_CHANNEL_3);
//    HAL_TIMEx_PWMN_Start(&htim1, TIM_CHANNEL_3);

    gADC.DelayApply = __HAL_TIM_GET_AUTORELOAD(&htim1) - DELAY_TIME;

    //HAL_ADCEx_MultiModeStart_DMA(&hadc1, gAdcResult, ADC_12_CHNNS);
    HAL_ADC_Start_DMA(&hadc1, (uint32_t *)gAdcResult, ADC_12_CHNNS);
    HAL_TIM_Base_Start_IT(&htim1);
    HAL_TIM_Base_Start(&htim4); //ĺźĺ?ĺŽTIM4
    DisableDrive();
    for (Ulong i = 0; i < 60000; i++);
    __HAL_TIM_CLEAR_FLAG(&htim1, TIM_FLAG_UPDATE);
    for (Ulong i = 0; i < 60000; i++);
}

//------------------------------------------------------------------------------------------------------------------------
//                                Interrupt Service
//------------------------------------------------------------------------------------------------------------------------
// PWM ISR
void HAL_TIM_PeriodElapsedCallback(TIM_HandleTypeDef *htim)
{
    if (htim == &htim1)
    {

    }
}

extern DMA_HandleTypeDef hdma_adc1;
/**
  * @brief This function handles DMA1 channel7 global interrupt.
  */
void DMA2_Channel1_IRQHandler(void)
{
  /* USER CODE BEGIN DMA1_Channel7_IRQn 0 */

  /* USER CODE END DMA1_Channel7_IRQn 0 */
  HAL_DMA_IRQHandler(&hdma_adc1);
  /* USER CODE BEGIN DMA1_Channel7_IRQn 1 */

  /* USER CODE END DMA1_Channel7_IRQn 1 */
}

int PtuTim1PWMTest(void)
{
    

}
#include "f_funcCode.h"
// ADC ISR
Uint gAdcTest;
extern uint8_t gDebugIpmPosTest;
extern void PC_M1_pcmasterdrvRecorder(void);
void HAL_ADC_ConvCpltCallback(ADC_HandleTypeDef *hadc)
{
    if (hadc != &hadc1)
        return;
    
    #ifdef  VFD_TEST_DEBUG
    if (PtuTimxPWMTest() == RT_TRUE)
        return;
    #endif
    
    DINT;

    gAdcTest++;
 
    if((nvs_datas.motor.BreakinAutomaticOutput == 1)
        && (LL_TIM_IsEnabledAutomaticOutput(TIM1) != 1))
        LL_TIM_EnableAutomaticOutput(TIM1);
    else if((nvs_datas.motor.BreakinAutomaticOutput == 0)
        && (LL_TIM_IsEnabledAutomaticOutput(TIM1) != 0))
        LL_TIM_DisableAutomaticOutput(TIM1);
    
    if((nvs_datas.motor.BreakinAutomaticOutput == 1) 
        && (gEnablePwmFlag == 1)
        && (LL_TIM_IsEnabledIT_BRK(TIM1) == 0))
    {
        LL_TIM_EnableIT_BRK(TIM1);
    }
    
    if(HAL_GPIO_ReadPin(GPIOE,GPIO_PIN_14) == GPIO_PIN_SET)
    {
        LL_DAC_ConvertData12RightAligned(DAC1,LL_DAC_CHANNEL_1,4095);  
        LL_DAC_ConvertData12RightAligned(DAC1,LL_DAC_CHANNEL_2,4095);  
    }
    else
    {
        LL_DAC_ConvertData12RightAligned(DAC1,LL_DAC_CHANNEL_1,0);
        LL_DAC_ConvertData12RightAligned(DAC1,LL_DAC_CHANNEL_2,0);
    }


	gIgbtBreake.ResetOver = 1;
    
    if (((0 == gMainCmd.Command.bit.Start) && (gBforeRunPhaseLose.ShortGndDetectFlag == 0)
        && (!gBforeRunPhaseLose.PowerOnLosePhaseDetectFlag) && (!gDebugIpmPosTest)) 
        || (gError.ErrorCode.all != 0)) //wujian 
    {
        DisableDrive();
    }

    if (hadc == &hadc1)
    {
        //get ADC sample Value
        gAdcRes_Iu  = (gAdcResult[0] >> 0) & 0xFFFF;  
        gAdcRes_Iv = (gAdcResult[1] >> 0) & 0xFFFF;  
        gAdcRes_Iw  = (gAdcResult[2] >> 0) & 0xFFFF;  
        gAdcRes_Udc  = (gAdcResult[3] >> 0) & 0xFFFF; 

       
        #ifdef  VFD_TEST_DEBUG
        if((vfd.manual.tim1_mask == 0)&&(vfd.manual.tim1_u_duty==0) && (vfd.manual.tim1_v_duty==0)&& (vfd.manual.tim1_w_duty==0))
        #endif
        gMainCmd.pADCIsr(); // çć?Łč°ç¨çä¸?ć?ĺ˝ć°
        

        gPWM.Uget = __HAL_TIM_GET_COMPARE(&htim1, TIM_CHANNEL_1);
        gPWM.Vget = __HAL_TIM_GET_COMPARE(&htim1, TIM_CHANNEL_2);
        gPWM.Wget = __HAL_TIM_GET_COMPARE(&htim1, TIM_CHANNEL_3);
        PC_M1_pcmasterdrvRecorder();
    }


    CC1 = gMainCmd.FreqSetApply;
//    CC2=gRatio;
//    CC3=gPWM.U;
    CC4 = gPhase.IMPhase;


    CC2 = gIMTQ12.M;
    CC3 = gIMTQ12.T;

    CC5 = gRotorSpeed.SpeedApply;

//    CC5 = gIAlphBetaQ12.Alph;
//    CC6 = gIAlphBetaQ12.Beta;

    CC6 = (gIAmpTheta.Amp * gMotorInfo.Current) >> 12;
//    CC5=gIUVWQ24.V>>12;
//    CC6=gIUVWQ24.W>>12;
//    CC7=gIUVWQ24.U>>12;
    CC7 = gIAmpTheta.Amp;

//    CC5 = gAdcRes_Iu;
//    CC6 = gAdcRes_Iv;
//    CC7 = gAdcRes_Iw;

//    CC5 = gExcursionInfo.IuValue;
//    CC6 = gExcursionInfo.IvValue;
//    CC7 = gExcursionInfo.IwValue;

    CC8 = gUDC.uDC;


    EINT;

}
