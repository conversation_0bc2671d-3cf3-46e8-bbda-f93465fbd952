/******************** (C) COPYRIGHT 2021     ***********************************
* File Name          : app_test.c
* Author             : xiou
* Version            : V1.0
* Date               :
* Description        :
********************************************************************************/
#include <rtthread.h>
#include <rtdevice.h>
#include "uapp.h"
#include <board.h>
#include "msh.h"

uint8_t ubAnalogWatchdog1Status = 0;
uint16_t awd_threshold_high = 0xFFF;
uint16_t awd_threshold_low = 1000;
void UserButton_Callback(void)
{
  /* Rearm ADC analog watchdog to be ready for another trig */

  LL_ADC_ConfigAnalogWDThresholds(ADC3, LL_ADC_AWD1, awd_threshold_high, awd_threshold_low);
  if(ubAnalogWatchdog1Status == 1)
  {
      ubAnalogWatchdog1Status = 0;
      /* Reset status variable of ADC analog watchdog 1 */
      ubAnalogWatchdog1Status = 0;
      
      /* Clear flag ADC analog watchdog 1 */
      LL_ADC_ClearFlag_AWD1(ADC3);
      
       /* Enable ADC analog watchdog 1 interruption */
      LL_ADC_EnableIT_AWD1(ADC3);
  }
}


void vfd_test_trigle_diag_record(void)
{
    static uint32_t tick = 0;
    
    return ; // test stop
    
    tick++;
    
    if(tick % 1000 == 0)
    {
        if((tick/1000) % 6 == 2)
            hardware_irq_pin.f_tim1_breakin_flag = 1;
        else if((tick/1000) % 6 == 3)
            hardware_irq_pin.fo1_flag = 1;
        else if((tick/1000) % 6 == 4)
            hardware_irq_pin.fo2_flag = 1;
        else if((tick/1000) % 6 == 5)
            hardware_irq_pin.fo3_flag = 1;
        else if((tick/1000) % 6 == 5)
            hardware_irq_pin.igbt_f_mcu1_flag = 1;
    }
}

void TIMx_PWM_Test(TIM_HandleTypeDef *htim, uint16_t dutyu,uint16_t dutyv,uint16_t dutyw)
{
    static uint16_t save_duty = 0;
    
    #ifdef VFD_TEST_DEBUG  // test pwm
    if(!vfd.ctrl.start_pfc &&  !vfd.ctrl.start_inv  && !vfd.ctrl.start)
    {
        if(!dutyu && !dutyv && !dutyw && (save_duty == htim->Instance))
        {
            save_duty = 0;
            LL_TIM_CC_DisableChannel(htim->Instance, (LL_TIM_CHANNEL_CH1|LL_TIM_CHANNEL_CH1N|\
                                                        LL_TIM_CHANNEL_CH2|LL_TIM_CHANNEL_CH2N|\
                                                        LL_TIM_CHANNEL_CH3|LL_TIM_CHANNEL_CH3N));
            LL_TIM_OC_SetCompareCH1(htim->Instance, 0);
            LL_TIM_OC_SetCompareCH2(htim->Instance, 0);
            LL_TIM_OC_SetCompareCH3(htim->Instance, 0);
            __HAL_TIM_MOE_DISABLE(htim);
        }
        
        if(dutyu )
        {
            /* Start Master PWM generation */
            LL_TIM_OC_SetCompareCH1(htim->Instance, dutyu*__HAL_TIM_GET_AUTORELOAD(htim)/100);
            LL_TIM_CC_EnableChannel(htim->Instance, (LL_TIM_CHANNEL_CH1|LL_TIM_CHANNEL_CH1N));
            /* Enable the Main Output */
            __HAL_TIM_MOE_ENABLE(htim);
            __HAL_TIM_ENABLE(htim);  
        }
        else
        {
            LL_TIM_CC_DisableChannel(htim->Instance, (LL_TIM_CHANNEL_CH1|LL_TIM_CHANNEL_CH1N));
            LL_TIM_OC_SetCompareCH1(htim->Instance, 0);
        }
        
        if(dutyv )
        {
            /* Start Master PWM generation */
            LL_TIM_OC_SetCompareCH2(htim->Instance, dutyv*__HAL_TIM_GET_AUTORELOAD(htim)/100);
            LL_TIM_CC_EnableChannel(htim->Instance, (LL_TIM_CHANNEL_CH2|LL_TIM_CHANNEL_CH2N));
            __HAL_TIM_MOE_ENABLE(htim);
            __HAL_TIM_ENABLE(htim);  
        }
        else
        {
            LL_TIM_CC_DisableChannel(htim->Instance, (LL_TIM_CHANNEL_CH2|LL_TIM_CHANNEL_CH2N));
            LL_TIM_OC_SetCompareCH2(htim->Instance, 0);
        }
        
        if(dutyw )
        {
            /* Start Master PWM generation */
            LL_TIM_OC_SetCompareCH3(htim->Instance, dutyw*__HAL_TIM_GET_AUTORELOAD(htim)/100);
            LL_TIM_CC_EnableChannel(htim->Instance, (LL_TIM_CHANNEL_CH3|LL_TIM_CHANNEL_CH3N));
            /* Enable the Main Output */
            __HAL_TIM_MOE_ENABLE(htim);
            __HAL_TIM_ENABLE(htim);  
        }
        else
        {
            LL_TIM_CC_DisableChannel(htim->Instance, (LL_TIM_CHANNEL_CH3|LL_TIM_CHANNEL_CH3N));
            LL_TIM_OC_SetCompareCH3(htim->Instance, 0);
        }
        
        if(dutyu || dutyv || dutyw)
        {
            save_duty = htim->Instance;
        }
    }
    #endif
}


void cmd_test(int argc, char **argv)
{

         
    rt_kprintf("  set %s to %d \r\n",argv[1],atoi(argv[2]));
    
}

#define TIMES   20000
#define PI      3.14f
extern __IO uint32_t tim2_irq_cnt;
/**
  * @name  FloatXFloat
  * @brief 单精度乘除法.
* @param angle：起始值，times：计算次数，*mod：1除法0乘法
  */
void FloatXFloat(float angle,uint32_t times,uint8_t mode)
{
    uint32_t i;
    float result;
    if(mode)//除法
    {
        for(i=0;i<times;i++)
        {
            result = angle/PI;
            angle += 0.00001f;
        }        
    }
    else//乘法
    {
        for(i=0;i<times;i++)
        {
            result = angle*PI;
            angle += 0.00001f;
        }
    }
}

/**
  * @name  DoubleD
  * @brief 双精度乘除法.
  * @param angle：起始值，times：计算次数，
*mod：1除法0乘法
  */
void DoubleD(double angle,uint32_t times,uint8_t mode)
{
    uint32_t i;
    double result;
    if(mode)//除法
    {
        for(i=0;i<times;i++)
        {
            result = angle/PI;
            angle += 0.00001f;
        }        
    }
    else//乘法
    {
        for(i=0;i<times;i++)
        {
            result = angle*PI;
            angle += 0.00001f;
        }
    }
}

uint8_t test_float_flag = 0;

void test_float_func(void)
{
    uint32_t start_tim,end_tim,tim_cnt;
    
    if(test_float_flag)
    {
        test_float_flag = 0;
        rt_kprintf("---------------NOT USER FPU----------------------\n");
        //-------------------测试单精度乘法----------------------------------------
        start_tim = HAL_GetTick();    
        //乘法
        FloatXFloat(PI/6,TIMES,0);      
        end_tim = HAL_GetTick();
        if(end_tim > start_tim)
        {
            tim_cnt = end_tim - start_tim;
        }
        else
        {
            tim_cnt = 0xffffffff - start_tim + end_tim;
        }
        rt_kprintf("单精度乘法-- %d ms\r\n",tim_cnt);    //显示运行时间  
        //-----------------测试单精度除法-------------------------------------------
        start_tim = HAL_GetTick();
        //除法
        FloatXFloat(PI/6,TIMES,1);
        end_tim = HAL_GetTick();
        if(end_tim > start_tim)
        {
            tim_cnt = end_tim - start_tim;
        }
        else
        {
            tim_cnt = 0xffffffff - start_tim + end_tim;
        }
        rt_kprintf("单精度除法-- %d ms\r\n",tim_cnt);    //显示运行时间 
        //-----------------测试双精度乘法-------------------------------------------
        start_tim = HAL_GetTick();
        //乘法
        DoubleD(PI/6,TIMES,1);
        end_tim = HAL_GetTick();
        if(end_tim > start_tim)
        {
            tim_cnt = end_tim - start_tim;
        }
        else
        {
            tim_cnt = 0xffffffff - start_tim + end_tim;
        }
        rt_kprintf("双精度乘法-- %d ms\r\n",tim_cnt);    //显示运行时间     
        //-----------------测试双精度除法-------------------------------------------
        start_tim = HAL_GetTick();
        //除法
        DoubleD(PI/6,TIMES,1);

        end_tim = HAL_GetTick();
        if(end_tim > start_tim)
        {
            tim_cnt = end_tim - start_tim;
        }
        else
        {
            tim_cnt = 0xffffffff - start_tim + end_tim;
        }
        rt_kprintf("双精度除法-- %d ms\r\n",tim_cnt);    //显示运行时间 
    }
}

void cmd_test_float(int argc, char **argv)
{
    test_float_flag = 1;
    
    test_float_func();
        
}
MSH_CMD_EXPORT_ALIAS(cmd_test_float,test_float, debug);

/*=======================================================*/
/**
 * @brief  use dma tx to fast printf,PC serialplot recv to run scope
 * @return  None.
 */
void FastPrintf_ToSerialPlot(void)
{
    if (vfd.disp_ctrl == 1)
    {
        #ifdef DEBUG_SERIAL_PLOT 
        static uint16_t print_cnt = 0;
        if((print_cnt == 0) && vfd.debug_picnt >= DEBUG_PLOT_POINTS)
        {
            print_cnt = DEBUG_PLOT_POINTS;
        }
        
        if(print_cnt > 0)
        {
            #if 1
            toolbox_dma_printf_ascii("b:%.3f,%.3f,%.3f\r\n" ,(vfd.debug_data1[DEBUG_PLOT_POINTS-print_cnt])
                                                        ,(vfd.debug_data2[DEBUG_PLOT_POINTS-print_cnt])
                                                        ,(vfd.debug_data3[DEBUG_PLOT_POINTS-print_cnt])); 
            
            //toolbox_dma_printf_ascii("b:%.3f\r\n" ,(vfd.debug_data1[DEBUG_PLOT_POINTS-print_cnt])); 
            #else
            char pData[64] = {0};
            
            rt_snprintf(pData,sizeof(pData),"b:%d,%d,%d,%d\r\n" ,((uint16_t)vfd.debug_data1[DEBUG_PLOT_POINTS-print_cnt])
                                                        ,((uint16_t)vfd.debug_data2[DEBUG_PLOT_POINTS-print_cnt])
                                                        ,((uint16_t)vfd.debug_data3[DEBUG_PLOT_POINTS-print_cnt])
                                                        ,((uint16_t)vfd.debug_data4[DEBUG_PLOT_POINTS-print_cnt]));
            
            rs485_send_frame(pData, rt_strlen(pData));
            
            #endif
            print_cnt--;
            
            if(print_cnt == 0)
              vfd.debug_picnt = 0;
        }
        #endif
    }
    else if (vfd.disp_ctrl == 2)
    {
        toolbox_dma_printf_ascii("b:%d,%d,%d\r\n",vfd.filter_ad.ac_iout_u,vfd.filter_ad.ac_iout_v,vfd.filter_ad.ac_iout_w);
    }
    else if (vfd.disp_ctrl == 3)
    {
        toolbox_dma_printf_ascii("b:%d,%d,%d\r\n",vfd.filter_ad.ac_vin_r, vfd.filter_ad.ac_vin_s,vfd.filter_ad.ac_vin_t,vfd.fast_ad.vbus_inv);
    }
    else if (vfd.disp_ctrl == 4)
    {
        toolbox_dma_printf_ascii("b:%d,%d,%d\r\n",ADC4_ValTemp[1],ADC4_ValTemp[2],ADC4_ValTemp[3]);
    }
}

void Test_IN1_Autoswitch(uint32_t div)
{
    static uint32_t tick = 0;
    
    tick++;
    
    if(tick % div == 10000)
    {
        vfd.manual.in1_mask = 1;
        vfd.manual.in1 = 1 - vfd.manual.in1;
    }
    
    if(vfd.filter_ad.vbus_inv >= 6450)
    {
        vfd.manual.in1_mask = 1;
        vfd.manual.in1 = 0;
        tick = 0;
    }
}

#include "finsh.h"

void cmd_modbus(int argc, char **argv)
{
    rt_kprintf(" modbus cnter \r\n");
    rt_kprintf(" modbus_rx_cnter :%d \r\n",vfd.modbus_rx_cnter);
    rt_kprintf(" modbus_tx_cnter :%d \r\n",vfd.modbus_tx_cnter);
    rt_kprintf(" modbus_tx_err_cnter :%d \r\n",vfd.modbus_tx_err_cnter);
    rt_kprintf(" modbus_rx_crc_err_cnter :%d \r\n",vfd.modbus_rx_crc_err_cnter);
    rt_kprintf(" modbus_rx_add_err_cnter :%d \r\n",vfd.modbus_rx_add_err_cnter);
    rt_kprintf(" modbus_rx_other_err_cnter :%d \r\n",vfd.modbus_rx_other_err_cnter);
    rt_kprintf(" modbus_tx_irq_cnter :%d \r\n",vfd.modbus_tx_irq_cnter);
    rt_kprintf(" rs485_rx_cnter :%d \r\n",vfd.rs485_rx_cnter);
    rt_kprintf(" modbus_rx_out_cnter :%d \r\n",vfd.modbus_rx_out_cnter);
    rt_kprintf(" ptu_rx_cnter :%d \r\n",vfd.ptu_rx_cnter);
    
    
    if((argc>1) && rt_strcmp(argv[1],"reset") == 0)
    {
        vfd.modbus_rx_cnter = 0;
        vfd.modbus_tx_cnter = 0;
        vfd.modbus_tx_err_cnter = 0;
        vfd.modbus_rx_crc_err_cnter = 0;
        vfd.modbus_rx_add_err_cnter = 0;
        vfd.modbus_rx_other_err_cnter = 0;
        vfd.modbus_tx_irq_cnter = 0;
        vfd.rs485_rx_cnter = 0;
        vfd.modbus_rx_out_cnter = 0;
        vfd.ptu_rx_cnter = 0;
    }
}
MSH_CMD_EXPORT_ALIAS(cmd_modbus,modbus, cmd_nvs set);
