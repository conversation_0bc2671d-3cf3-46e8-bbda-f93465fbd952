<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<ProjectOpt xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="project_optx.xsd">

  <SchemaVersion>1.0</SchemaVersion>

  <Header>### uVision Project, (C) Keil Software</Header>

  <Extensions>
    <cExt>*.c</cExt>
    <aExt>*.s*; *.src; *.a*</aExt>
    <oExt>*.obj; *.o</oExt>
    <lExt>*.lib</lExt>
    <tExt>*.txt; *.h; *.inc; *.md</tExt>
    <pExt>*.plm</pExt>
    <CppX>*.cpp; *.cc; *.cxx</CppX>
    <nMigrate>0</nMigrate>
  </Extensions>

  <DaveTm>
    <dwLowDateTime>0</dwLowDateTime>
    <dwHighDateTime>0</dwHighDateTime>
  </DaveTm>

  <Target>
    <TargetName>PVPB09</TargetName>
    <ToolsetNumber>0x4</ToolsetNumber>
    <ToolsetName>ARM-ADS</ToolsetName>
    <TargetOption>
      <CLKADS>12000000</CLKADS>
      <OPTTT>
        <gFlags>1</gFlags>
        <BeepAtEnd>1</BeepAtEnd>
        <RunSim>0</RunSim>
        <RunTarget>1</RunTarget>
        <RunAbUc>0</RunAbUc>
      </OPTTT>
      <OPTHX>
        <HexSelection>1</HexSelection>
        <FlashByte>65535</FlashByte>
        <HexRangeLowAddress>0</HexRangeLowAddress>
        <HexRangeHighAddress>0</HexRangeHighAddress>
        <HexOffset>0</HexOffset>
      </OPTHX>
      <OPTLEX>
        <PageWidth>79</PageWidth>
        <PageLength>66</PageLength>
        <TabStop>8</TabStop>
        <ListingPath>..\..\..\..\build\inv\list\</ListingPath>
      </OPTLEX>
      <ListingPage>
        <CreateCListing>1</CreateCListing>
        <CreateAListing>1</CreateAListing>
        <CreateLListing>1</CreateLListing>
        <CreateIListing>0</CreateIListing>
        <AsmCond>1</AsmCond>
        <AsmSymb>1</AsmSymb>
        <AsmXref>0</AsmXref>
        <CCond>1</CCond>
        <CCode>0</CCode>
        <CListInc>0</CListInc>
        <CSymb>0</CSymb>
        <LinkerCodeListing>0</LinkerCodeListing>
      </ListingPage>
      <OPTXL>
        <LMap>1</LMap>
        <LComments>1</LComments>
        <LGenerateSymbols>1</LGenerateSymbols>
        <LLibSym>1</LLibSym>
        <LLines>1</LLines>
        <LLocSym>1</LLocSym>
        <LPubSym>1</LPubSym>
        <LXref>0</LXref>
        <LExpSel>0</LExpSel>
      </OPTXL>
      <OPTFL>
        <tvExp>1</tvExp>
        <tvExpOptDlg>0</tvExpOptDlg>
        <IsCurrentTarget>0</IsCurrentTarget>
      </OPTFL>
      <CpuCode>18</CpuCode>
      <DebugOpt>
        <uSim>0</uSim>
        <uTrg>1</uTrg>
        <sLdApp>1</sLdApp>
        <sGomain>1</sGomain>
        <sRbreak>1</sRbreak>
        <sRwatch>1</sRwatch>
        <sRmem>1</sRmem>
        <sRfunc>1</sRfunc>
        <sRbox>1</sRbox>
        <tLdApp>1</tLdApp>
        <tGomain>1</tGomain>
        <tRbreak>1</tRbreak>
        <tRwatch>1</tRwatch>
        <tRmem>1</tRmem>
        <tRfunc>0</tRfunc>
        <tRbox>1</tRbox>
        <tRtrace>1</tRtrace>
        <sRSysVw>1</sRSysVw>
        <tRSysVw>1</tRSysVw>
        <sRunDeb>0</sRunDeb>
        <sLrtime>0</sLrtime>
        <bEvRecOn>1</bEvRecOn>
        <bSchkAxf>0</bSchkAxf>
        <bTchkAxf>0</bTchkAxf>
        <nTsel>6</nTsel>
        <sDll></sDll>
        <sDllPa></sDllPa>
        <sDlgDll></sDlgDll>
        <sDlgPa></sDlgPa>
        <sIfile></sIfile>
        <tDll></tDll>
        <tDllPa></tDllPa>
        <tDlgDll></tDlgDll>
        <tDlgPa></tDlgPa>
        <tIfile></tIfile>
        <pMon>STLink\ST-LINKIII-KEIL_SWO.dll</pMon>
      </DebugOpt>
      <TargetDriverDllRegistry>
        <SetRegEntry>
          <Number>0</Number>
          <Key>ST-LINKIII-KEIL_SWO</Key>
          <Name>-U00470044320000024E575359 -O206 -SF2000 -C0 -A0 -I0 -HNlocalhost -HP7184 -P1 -N00("ARM CoreSight SW-DP (ARM Core") -D00(2BA01477) -L00(0) -TO65554 -********** -********** -TP21 -TDS8007 -TDT0 -TDC1F -TIEFFFFFFFF -TIP8 -FO3 -********** -FC1000 -FN1 -FF0STM32G4xx_512.FLM -********** -FL080000 -FP0($$Device:STM32G474QETx$CMSIS\Flash\STM32G4xx_512.FLM)</Name>
        </SetRegEntry>
        <SetRegEntry>
          <Number>0</Number>
          <Key>JL2CM3</Key>
          <Name>-********* -O206 -S3 -ZTIFSpeedSel3000 -A0 -C0 -JU1 -JI127.0.0.1 -JP0 -RST0 -N00("ARM CoreSight SW-DP") -D00(2BA01477) -L00(0) -TO65554 -********** -TP21 -TDS8007 -TDT0 -TDC1F -TIEFFFFFFFF -TIP8 -TB1 -TFE0 -FO3 -********** -FC1000 -FN1 -FF0STM32G4xx_512.FLM -********** -FL080000 -FP0($$Device:STM32G474QETx$CMSIS\Flash\STM32G4xx_512.FLM)</Name>
        </SetRegEntry>
        <SetRegEntry>
          <Number>0</Number>
          <Key>UL2CM3</Key>
          <Name>-UAny -O206 -S10 -C0 -P00 -N00("ARM CoreSight SW-DP") -D00(2BA01477) -L00(0) -TO65554 -********** -********** -TP21 -TDS8007 -TDT0 -TDC1F -TIEFFFFFFFF -TIP8 -FO3  -FN1 -FC1000 -********** -FF0STM32G4xx_512 -FL080000 -********** -FP0($$Device:STM32G474QETx$CMSIS\Flash\STM32G4xx_512.FLM)</Name>
        </SetRegEntry>
        <SetRegEntry>
          <Number>0</Number>
          <Key>ARMRTXEVENTFLAGS</Key>
          <Name>-L70 -Z18 -C0 -M0 -T1</Name>
        </SetRegEntry>
        <SetRegEntry>
          <Number>0</Number>
          <Key>DLGTARM</Key>
          <Name>(1010=-1,-1,-1,-1,0)(1007=-1,-1,-1,-1,0)(1008=-1,-1,-1,-1,0)(1009=-1,-1,-1,-1,0)(1012=-1,-1,-1,-1,0)</Name>
        </SetRegEntry>
        <SetRegEntry>
          <Number>0</Number>
          <Key>ARMDBGFLAGS</Key>
          <Name></Name>
        </SetRegEntry>
        <SetRegEntry>
          <Number>0</Number>
          <Key>DLGUARM</Key>
          <Name>(105=-1,-1,-1,-1,0)</Name>
        </SetRegEntry>
      </TargetDriverDllRegistry>
      <Breakpoint/>
      <WatchWindow1>
        <Ww>
          <count>0</count>
          <WinNumber>1</WinNumber>
          <ItemText>ADC1_ValTemp,0x0A</ItemText>
        </Ww>
        <Ww>
          <count>1</count>
          <WinNumber>1</WinNumber>
          <ItemText>dcvin_save</ItemText>
        </Ww>
        <Ww>
          <count>2</count>
          <WinNumber>1</WinNumber>
          <ItemText>dcvin_save</ItemText>
        </Ww>
        <Ww>
          <count>3</count>
          <WinNumber>1</WinNumber>
          <ItemText>aciout_v</ItemText>
        </Ww>
        <Ww>
          <count>4</count>
          <WinNumber>1</WinNumber>
          <ItemText>vfd.diag,0x0A</ItemText>
        </Ww>
        <Ww>
          <count>5</count>
          <WinNumber>1</WinNumber>
          <ItemText>pRecvStream,0x0A</ItemText>
        </Ww>
        <Ww>
          <count>6</count>
          <WinNumber>1</WinNumber>
          <ItemText>pSendStream</ItemText>
        </Ww>
        <Ww>
          <count>7</count>
          <WinNumber>1</WinNumber>
          <ItemText>dirent</ItemText>
        </Ww>
        <Ww>
          <count>8</count>
          <WinNumber>1</WinNumber>
          <ItemText>file</ItemText>
        </Ww>
        <Ww>
          <count>9</count>
          <WinNumber>1</WinNumber>
          <ItemText>shell,0x0A</ItemText>
        </Ww>
        <Ww>
          <count>10</count>
          <WinNumber>1</WinNumber>
          <ItemText>bsm_com_port,0x0A</ItemText>
        </Ww>
        <Ww>
          <count>11</count>
          <WinNumber>1</WinNumber>
          <ItemText>com_set_as</ItemText>
        </Ww>
        <Ww>
          <count>12</count>
          <WinNumber>1</WinNumber>
          <ItemText>com_server,0x0A</ItemText>
        </Ww>
        <Ww>
          <count>13</count>
          <WinNumber>1</WinNumber>
          <ItemText>gAdcResult</ItemText>
        </Ww>
      </WatchWindow1>
      <WatchWindow2>
        <Ww>
          <count>0</count>
          <WinNumber>2</WinNumber>
          <ItemText>debug_81</ItemText>
        </Ww>
        <Ww>
          <count>1</count>
          <WinNumber>2</WinNumber>
          <ItemText>ADC5_ValTemp,0x0A</ItemText>
        </Ww>
        <Ww>
          <count>2</count>
          <WinNumber>2</WinNumber>
          <ItemText>ac_trans</ItemText>
        </Ww>
        <Ww>
          <count>3</count>
          <WinNumber>2</WinNumber>
          <ItemText>DPLL3P.Vac</ItemText>
        </Ww>
        <Ww>
          <count>4</count>
          <WinNumber>2</WinNumber>
          <ItemText>DPLL3P</ItemText>
        </Ww>
        <Ww>
          <count>5</count>
          <WinNumber>2</WinNumber>
          <ItemText>vfd,0x0A</ItemText>
        </Ww>
        <Ww>
          <count>6</count>
          <WinNumber>2</WinNumber>
          <ItemText>AD_Origine_FL,0x0A</ItemText>
        </Ww>
        <Ww>
          <count>7</count>
          <WinNumber>2</WinNumber>
          <ItemText>ADC1_ValTemp[1]</ItemText>
        </Ww>
        <Ww>
          <count>8</count>
          <WinNumber>2</WinNumber>
          <ItemText>vfd.SetAdCa,0x0A</ItemText>
        </Ww>
        <Ww>
          <count>9</count>
          <WinNumber>2</WinNumber>
          <ItemText>vfd.diag,0x0A</ItemText>
        </Ww>
        <Ww>
          <count>10</count>
          <WinNumber>2</WinNumber>
          <ItemText>vfd.analog.vbus_inv,0x0A</ItemText>
        </Ww>
        <Ww>
          <count>11</count>
          <WinNumber>2</WinNumber>
          <ItemText>EPSET.System_work_freq</ItemText>
        </Ww>
        <Ww>
          <count>12</count>
          <WinNumber>2</WinNumber>
          <ItemText>vfd.abs_fast_acin_freq</ItemText>
        </Ww>
        <Ww>
          <count>13</count>
          <WinNumber>2</WinNumber>
          <ItemText>vfd.manual.kmon3_mask,0x0A</ItemText>
        </Ww>
        <Ww>
          <count>14</count>
          <WinNumber>2</WinNumber>
          <ItemText>vfd.manual.kmon3,0x0A</ItemText>
        </Ww>
        <Ww>
          <count>15</count>
          <WinNumber>2</WinNumber>
          <ItemText>dio_table,0x0A</ItemText>
        </Ww>
        <Ww>
          <count>16</count>
          <WinNumber>2</WinNumber>
          <ItemText>nvs_datas.modbus.reg_3000,0x0A</ItemText>
        </Ww>
        <Ww>
          <count>17</count>
          <WinNumber>2</WinNumber>
          <ItemText>can_ptu_rx_cnt,0x0A</ItemText>
        </Ww>
        <Ww>
          <count>18</count>
          <WinNumber>2</WinNumber>
          <ItemText>com_ptu,0x0A</ItemText>
        </Ww>
        <Ww>
          <count>19</count>
          <WinNumber>2</WinNumber>
          <ItemText>rxcnter,0x0A</ItemText>
        </Ww>
        <Ww>
          <count>20</count>
          <WinNumber>2</WinNumber>
          <ItemText>fdcan1_init_flag</ItemText>
        </Ww>
        <Ww>
          <count>21</count>
          <WinNumber>2</WinNumber>
          <ItemText>vfd.SetAdCa,0x0A</ItemText>
        </Ww>
        <Ww>
          <count>22</count>
          <WinNumber>2</WinNumber>
          <ItemText>ADC1_ValTemp</ItemText>
        </Ww>
        <Ww>
          <count>23</count>
          <WinNumber>2</WinNumber>
          <ItemText>ADC2_ValTemp,0x0A</ItemText>
        </Ww>
        <Ww>
          <count>24</count>
          <WinNumber>2</WinNumber>
          <ItemText>ADC3_ValTemp,0x0A</ItemText>
        </Ww>
        <Ww>
          <count>25</count>
          <WinNumber>2</WinNumber>
          <ItemText>ADC4_ValTemp</ItemText>
        </Ww>
        <Ww>
          <count>26</count>
          <WinNumber>2</WinNumber>
          <ItemText>ADC5_ValTemp</ItemText>
        </Ww>
      </WatchWindow2>
      <MemoryWindow1>
        <Mm>
          <WinNumber>1</WinNumber>
          <SubType>0</SubType>
          <ItemText>pData</ItemText>
          <AccSizeX>0</AccSizeX>
        </Mm>
      </MemoryWindow1>
      <MemoryWindow2>
        <Mm>
          <WinNumber>2</WinNumber>
          <SubType>1</SubType>
          <ItemText>0x20015770</ItemText>
          <AccSizeX>0</AccSizeX>
        </Mm>
      </MemoryWindow2>
      <MemoryWindow3>
        <Mm>
          <WinNumber>3</WinNumber>
          <SubType>0</SubType>
          <ItemText>ADC1_ValTemp</ItemText>
          <AccSizeX>0</AccSizeX>
        </Mm>
      </MemoryWindow3>
      <Tracepoint>
        <THDelay>0</THDelay>
      </Tracepoint>
      <DebugFlag>
        <trace>0</trace>
        <periodic>1</periodic>
        <aLwin>0</aLwin>
        <aCover>0</aCover>
        <aSer1>0</aSer1>
        <aSer2>0</aSer2>
        <aPa>0</aPa>
        <viewmode>1</viewmode>
        <vrSel>0</vrSel>
        <aSym>0</aSym>
        <aTbox>0</aTbox>
        <AscS1>0</AscS1>
        <AscS2>0</AscS2>
        <AscS3>0</AscS3>
        <aSer3>0</aSer3>
        <eProf>0</eProf>
        <aLa>0</aLa>
        <aPa1>0</aPa1>
        <AscS4>0</AscS4>
        <aSer4>0</aSer4>
        <StkLoc>1</StkLoc>
        <TrcWin>0</TrcWin>
        <newCpu>0</newCpu>
        <uProt>0</uProt>
      </DebugFlag>
      <LintExecutable></LintExecutable>
      <LintConfigFile></LintConfigFile>
      <bLintAuto>0</bLintAuto>
      <bAutoGenD>0</bAutoGenD>
      <LntExFlags>0</LntExFlags>
      <pMisraName></pMisraName>
      <pszMrule></pszMrule>
      <pSingCmds></pSingCmds>
      <pMultCmds></pMultCmds>
      <pMisraNamep></pMisraNamep>
      <pszMrulep></pszMrulep>
      <pSingCmdsp></pSingCmdsp>
      <pMultCmdsp></pMultCmdsp>
      <SystemViewers>
        <Entry>
          <Name>System Viewer\TIM1</Name>
          <WinId>35905</WinId>
        </Entry>
      </SystemViewers>
      <DebugDescription>
        <Enable>1</Enable>
        <EnableFlashSeq>0</EnableFlashSeq>
        <EnableLog>0</EnableLog>
        <Protocol>2</Protocol>
        <DbgClock>2000000</DbgClock>
      </DebugDescription>
    </TargetOption>
  </Target>

  <Target>
    <TargetName>PVPB09_例行测试</TargetName>
    <ToolsetNumber>0x4</ToolsetNumber>
    <ToolsetName>ARM-ADS</ToolsetName>
    <TargetOption>
      <CLKADS>12000000</CLKADS>
      <OPTTT>
        <gFlags>1</gFlags>
        <BeepAtEnd>1</BeepAtEnd>
        <RunSim>0</RunSim>
        <RunTarget>1</RunTarget>
        <RunAbUc>0</RunAbUc>
      </OPTTT>
      <OPTHX>
        <HexSelection>1</HexSelection>
        <FlashByte>65535</FlashByte>
        <HexRangeLowAddress>0</HexRangeLowAddress>
        <HexRangeHighAddress>0</HexRangeHighAddress>
        <HexOffset>0</HexOffset>
      </OPTHX>
      <OPTLEX>
        <PageWidth>79</PageWidth>
        <PageLength>66</PageLength>
        <TabStop>8</TabStop>
        <ListingPath>..\..\..\..\build\inv\list\</ListingPath>
      </OPTLEX>
      <ListingPage>
        <CreateCListing>1</CreateCListing>
        <CreateAListing>1</CreateAListing>
        <CreateLListing>1</CreateLListing>
        <CreateIListing>0</CreateIListing>
        <AsmCond>1</AsmCond>
        <AsmSymb>1</AsmSymb>
        <AsmXref>0</AsmXref>
        <CCond>1</CCond>
        <CCode>0</CCode>
        <CListInc>0</CListInc>
        <CSymb>0</CSymb>
        <LinkerCodeListing>0</LinkerCodeListing>
      </ListingPage>
      <OPTXL>
        <LMap>1</LMap>
        <LComments>1</LComments>
        <LGenerateSymbols>1</LGenerateSymbols>
        <LLibSym>1</LLibSym>
        <LLines>1</LLines>
        <LLocSym>1</LLocSym>
        <LPubSym>1</LPubSym>
        <LXref>0</LXref>
        <LExpSel>0</LExpSel>
      </OPTXL>
      <OPTFL>
        <tvExp>1</tvExp>
        <tvExpOptDlg>0</tvExpOptDlg>
        <IsCurrentTarget>1</IsCurrentTarget>
      </OPTFL>
      <CpuCode>18</CpuCode>
      <DebugOpt>
        <uSim>0</uSim>
        <uTrg>1</uTrg>
        <sLdApp>1</sLdApp>
        <sGomain>1</sGomain>
        <sRbreak>1</sRbreak>
        <sRwatch>1</sRwatch>
        <sRmem>1</sRmem>
        <sRfunc>1</sRfunc>
        <sRbox>1</sRbox>
        <tLdApp>1</tLdApp>
        <tGomain>1</tGomain>
        <tRbreak>1</tRbreak>
        <tRwatch>1</tRwatch>
        <tRmem>1</tRmem>
        <tRfunc>0</tRfunc>
        <tRbox>1</tRbox>
        <tRtrace>1</tRtrace>
        <sRSysVw>1</sRSysVw>
        <tRSysVw>1</tRSysVw>
        <sRunDeb>0</sRunDeb>
        <sLrtime>0</sLrtime>
        <bEvRecOn>1</bEvRecOn>
        <bSchkAxf>0</bSchkAxf>
        <bTchkAxf>0</bTchkAxf>
        <nTsel>6</nTsel>
        <sDll></sDll>
        <sDllPa></sDllPa>
        <sDlgDll></sDlgDll>
        <sDlgPa></sDlgPa>
        <sIfile></sIfile>
        <tDll></tDll>
        <tDllPa></tDllPa>
        <tDlgDll></tDlgDll>
        <tDlgPa></tDlgPa>
        <tIfile></tIfile>
        <pMon>STLink\ST-LINKIII-KEIL_SWO.dll</pMon>
      </DebugOpt>
      <TargetDriverDllRegistry>
        <SetRegEntry>
          <Number>0</Number>
          <Key>ST-LINKIII-KEIL_SWO</Key>
          <Name>-U00470044320000024E575359 -O206 -SF2000 -C0 -A0 -I0 -HNlocalhost -HP7184 -P1 -N00("ARM CoreSight SW-DP (ARM Core") -D00(2BA01477) -L00(0) -TO65554 -********** -********** -TP21 -TDS8007 -TDT0 -TDC1F -TIEFFFFFFFF -TIP8 -FO3 -********** -FC1000 -FN1 -FF0STM32G4xx_512.FLM -********** -FL080000 -FP0($$Device:STM32G474QETx$CMSIS\Flash\STM32G4xx_512.FLM)</Name>
        </SetRegEntry>
        <SetRegEntry>
          <Number>0</Number>
          <Key>JL2CM3</Key>
          <Name>-********* -O206 -S3 -ZTIFSpeedSel3000 -A0 -C0 -JU1 -JI127.0.0.1 -JP0 -RST0 -N00("ARM CoreSight SW-DP") -D00(2BA01477) -L00(0) -TO65554 -********** -TP21 -TDS8007 -TDT0 -TDC1F -TIEFFFFFFFF -TIP8 -TB1 -TFE0 -FO3 -********** -FC1000 -FN1 -FF0STM32G4xx_512.FLM -********** -FL080000 -FP0($$Device:STM32G474QETx$CMSIS\Flash\STM32G4xx_512.FLM)</Name>
        </SetRegEntry>
        <SetRegEntry>
          <Number>0</Number>
          <Key>UL2CM3</Key>
          <Name>-UAny -O206 -S10 -C0 -P00 -N00("ARM CoreSight SW-DP") -D00(2BA01477) -L00(0) -TO65554 -********** -********** -TP21 -TDS8007 -TDT0 -TDC1F -TIEFFFFFFFF -TIP8 -FO3  -FN1 -FC1000 -********** -FF0STM32G4xx_512 -FL080000 -********** -FP0($$Device:STM32G474QETx$CMSIS\Flash\STM32G4xx_512.FLM)</Name>
        </SetRegEntry>
        <SetRegEntry>
          <Number>0</Number>
          <Key>ARMRTXEVENTFLAGS</Key>
          <Name>-L70 -Z18 -C0 -M0 -T1</Name>
        </SetRegEntry>
        <SetRegEntry>
          <Number>0</Number>
          <Key>DLGTARM</Key>
          <Name>(1010=-1,-1,-1,-1,0)(1007=-1,-1,-1,-1,0)(1008=-1,-1,-1,-1,0)(1009=-1,-1,-1,-1,0)(1012=-1,-1,-1,-1,0)</Name>
        </SetRegEntry>
        <SetRegEntry>
          <Number>0</Number>
          <Key>ARMDBGFLAGS</Key>
          <Name></Name>
        </SetRegEntry>
        <SetRegEntry>
          <Number>0</Number>
          <Key>DLGUARM</Key>
          <Name>(105=-1,-1,-1,-1,0)</Name>
        </SetRegEntry>
      </TargetDriverDllRegistry>
      <Breakpoint/>
      <WatchWindow1>
        <Ww>
          <count>0</count>
          <WinNumber>1</WinNumber>
          <ItemText>ADC1_ValTemp,0x0A</ItemText>
        </Ww>
        <Ww>
          <count>1</count>
          <WinNumber>1</WinNumber>
          <ItemText>dcvin_save</ItemText>
        </Ww>
        <Ww>
          <count>2</count>
          <WinNumber>1</WinNumber>
          <ItemText>dcvin_save</ItemText>
        </Ww>
        <Ww>
          <count>3</count>
          <WinNumber>1</WinNumber>
          <ItemText>aciout_v</ItemText>
        </Ww>
        <Ww>
          <count>4</count>
          <WinNumber>1</WinNumber>
          <ItemText>vfd.diag,0x0A</ItemText>
        </Ww>
        <Ww>
          <count>5</count>
          <WinNumber>1</WinNumber>
          <ItemText>pRecvStream,0x0A</ItemText>
        </Ww>
        <Ww>
          <count>6</count>
          <WinNumber>1</WinNumber>
          <ItemText>pSendStream</ItemText>
        </Ww>
        <Ww>
          <count>7</count>
          <WinNumber>1</WinNumber>
          <ItemText>dirent</ItemText>
        </Ww>
        <Ww>
          <count>8</count>
          <WinNumber>1</WinNumber>
          <ItemText>file</ItemText>
        </Ww>
        <Ww>
          <count>9</count>
          <WinNumber>1</WinNumber>
          <ItemText>shell,0x0A</ItemText>
        </Ww>
        <Ww>
          <count>10</count>
          <WinNumber>1</WinNumber>
          <ItemText>bsm_com_port,0x0A</ItemText>
        </Ww>
        <Ww>
          <count>11</count>
          <WinNumber>1</WinNumber>
          <ItemText>com_set_as</ItemText>
        </Ww>
        <Ww>
          <count>12</count>
          <WinNumber>1</WinNumber>
          <ItemText>com_server,0x0A</ItemText>
        </Ww>
        <Ww>
          <count>13</count>
          <WinNumber>1</WinNumber>
          <ItemText>gAdcResult</ItemText>
        </Ww>
        <Ww>
          <count>14</count>
          <WinNumber>1</WinNumber>
          <ItemText>pfc.use_tick,0x0A</ItemText>
        </Ww>
        <Ww>
          <count>15</count>
          <WinNumber>1</WinNumber>
          <ItemText>can_slave.slave_period,0x0A</ItemText>
        </Ww>
        <Ww>
          <count>16</count>
          <WinNumber>1</WinNumber>
          <ItemText>vfd</ItemText>
        </Ww>
        <Ww>
          <count>17</count>
          <WinNumber>1</WinNumber>
          <ItemText>_tick_cnt,0x0A</ItemText>
        </Ww>
        <Ww>
          <count>18</count>
          <WinNumber>1</WinNumber>
          <ItemText>vfd.manual.timx_Fan_mask,0x0A</ItemText>
        </Ww>
        <Ww>
          <count>19</count>
          <WinNumber>1</WinNumber>
          <ItemText>vfd.manual.timx_fan_duty,0x0A</ItemText>
        </Ww>
        <Ww>
          <count>20</count>
          <WinNumber>1</WinNumber>
          <ItemText>vfd.fan_duty,0x0A</ItemText>
        </Ww>
      </WatchWindow1>
      <WatchWindow2>
        <Ww>
          <count>0</count>
          <WinNumber>2</WinNumber>
          <ItemText>debug_81</ItemText>
        </Ww>
        <Ww>
          <count>1</count>
          <WinNumber>2</WinNumber>
          <ItemText>ADC5_ValTemp,0x0A</ItemText>
        </Ww>
        <Ww>
          <count>2</count>
          <WinNumber>2</WinNumber>
          <ItemText>ac_trans</ItemText>
        </Ww>
        <Ww>
          <count>3</count>
          <WinNumber>2</WinNumber>
          <ItemText>DPLL3P.Vac</ItemText>
        </Ww>
        <Ww>
          <count>4</count>
          <WinNumber>2</WinNumber>
          <ItemText>DPLL3P</ItemText>
        </Ww>
        <Ww>
          <count>5</count>
          <WinNumber>2</WinNumber>
          <ItemText>vfd,0x0A</ItemText>
        </Ww>
        <Ww>
          <count>6</count>
          <WinNumber>2</WinNumber>
          <ItemText>AD_Origine_FL,0x0A</ItemText>
        </Ww>
        <Ww>
          <count>7</count>
          <WinNumber>2</WinNumber>
          <ItemText>ADC1_ValTemp[1]</ItemText>
        </Ww>
        <Ww>
          <count>8</count>
          <WinNumber>2</WinNumber>
          <ItemText>vfd.SetAdCa,0x0A</ItemText>
        </Ww>
        <Ww>
          <count>9</count>
          <WinNumber>2</WinNumber>
          <ItemText>vfd.diag,0x0A</ItemText>
        </Ww>
        <Ww>
          <count>10</count>
          <WinNumber>2</WinNumber>
          <ItemText>vfd.analog.vbus_inv,0x0A</ItemText>
        </Ww>
        <Ww>
          <count>11</count>
          <WinNumber>2</WinNumber>
          <ItemText>EPSET.System_work_freq</ItemText>
        </Ww>
        <Ww>
          <count>12</count>
          <WinNumber>2</WinNumber>
          <ItemText>vfd.abs_fast_acin_freq</ItemText>
        </Ww>
        <Ww>
          <count>13</count>
          <WinNumber>2</WinNumber>
          <ItemText>vfd.manual.kmon3_mask,0x0A</ItemText>
        </Ww>
        <Ww>
          <count>14</count>
          <WinNumber>2</WinNumber>
          <ItemText>vfd.manual.kmon3,0x0A</ItemText>
        </Ww>
        <Ww>
          <count>15</count>
          <WinNumber>2</WinNumber>
          <ItemText>dio_table,0x0A</ItemText>
        </Ww>
        <Ww>
          <count>16</count>
          <WinNumber>2</WinNumber>
          <ItemText>nvs_datas.modbus.reg_3000,0x0A</ItemText>
        </Ww>
        <Ww>
          <count>17</count>
          <WinNumber>2</WinNumber>
          <ItemText>can_ptu_rx_cnt,0x0A</ItemText>
        </Ww>
        <Ww>
          <count>18</count>
          <WinNumber>2</WinNumber>
          <ItemText>com_ptu,0x0A</ItemText>
        </Ww>
        <Ww>
          <count>19</count>
          <WinNumber>2</WinNumber>
          <ItemText>rxcnter,0x0A</ItemText>
        </Ww>
        <Ww>
          <count>20</count>
          <WinNumber>2</WinNumber>
          <ItemText>fdcan1_init_flag</ItemText>
        </Ww>
        <Ww>
          <count>21</count>
          <WinNumber>2</WinNumber>
          <ItemText>vfd.SetAdCa,0x0A</ItemText>
        </Ww>
        <Ww>
          <count>22</count>
          <WinNumber>2</WinNumber>
          <ItemText>ADC1_ValTemp</ItemText>
        </Ww>
        <Ww>
          <count>23</count>
          <WinNumber>2</WinNumber>
          <ItemText>ADC2_ValTemp,0x0A</ItemText>
        </Ww>
        <Ww>
          <count>24</count>
          <WinNumber>2</WinNumber>
          <ItemText>ADC3_ValTemp,0x0A</ItemText>
        </Ww>
        <Ww>
          <count>25</count>
          <WinNumber>2</WinNumber>
          <ItemText>ADC4_ValTemp</ItemText>
        </Ww>
        <Ww>
          <count>26</count>
          <WinNumber>2</WinNumber>
          <ItemText>ADC5_ValTemp</ItemText>
        </Ww>
      </WatchWindow2>
      <MemoryWindow1>
        <Mm>
          <WinNumber>1</WinNumber>
          <SubType>0</SubType>
          <ItemText>pData</ItemText>
          <AccSizeX>0</AccSizeX>
        </Mm>
      </MemoryWindow1>
      <MemoryWindow2>
        <Mm>
          <WinNumber>2</WinNumber>
          <SubType>1</SubType>
          <ItemText>0x20015770</ItemText>
          <AccSizeX>0</AccSizeX>
        </Mm>
      </MemoryWindow2>
      <MemoryWindow3>
        <Mm>
          <WinNumber>3</WinNumber>
          <SubType>0</SubType>
          <ItemText>ADC1_ValTemp</ItemText>
          <AccSizeX>0</AccSizeX>
        </Mm>
      </MemoryWindow3>
      <Tracepoint>
        <THDelay>0</THDelay>
      </Tracepoint>
      <DebugFlag>
        <trace>0</trace>
        <periodic>1</periodic>
        <aLwin>0</aLwin>
        <aCover>0</aCover>
        <aSer1>0</aSer1>
        <aSer2>0</aSer2>
        <aPa>0</aPa>
        <viewmode>1</viewmode>
        <vrSel>0</vrSel>
        <aSym>0</aSym>
        <aTbox>0</aTbox>
        <AscS1>0</AscS1>
        <AscS2>0</AscS2>
        <AscS3>0</AscS3>
        <aSer3>0</aSer3>
        <eProf>0</eProf>
        <aLa>0</aLa>
        <aPa1>0</aPa1>
        <AscS4>0</AscS4>
        <aSer4>0</aSer4>
        <StkLoc>1</StkLoc>
        <TrcWin>0</TrcWin>
        <newCpu>0</newCpu>
        <uProt>0</uProt>
      </DebugFlag>
      <LintExecutable></LintExecutable>
      <LintConfigFile></LintConfigFile>
      <bLintAuto>0</bLintAuto>
      <bAutoGenD>0</bAutoGenD>
      <LntExFlags>0</LntExFlags>
      <pMisraName></pMisraName>
      <pszMrule></pszMrule>
      <pSingCmds></pSingCmds>
      <pMultCmds></pMultCmds>
      <pMisraNamep></pMisraNamep>
      <pszMrulep></pszMrulep>
      <pSingCmdsp></pSingCmdsp>
      <pMultCmdsp></pMultCmdsp>
      <SystemViewers>
        <Entry>
          <Name>System Viewer\TIM1</Name>
          <WinId>35904</WinId>
        </Entry>
        <Entry>
          <Name>System Viewer\TIM8</Name>
          <WinId>35905</WinId>
        </Entry>
      </SystemViewers>
      <DebugDescription>
        <Enable>1</Enable>
        <EnableFlashSeq>0</EnableFlashSeq>
        <EnableLog>0</EnableLog>
        <Protocol>2</Protocol>
        <DbgClock>2000000</DbgClock>
      </DebugDescription>
    </TargetOption>
  </Target>

  <Group>
    <GroupName>extcomm/sifang</GroupName>
    <tvExp>1</tvExp>
    <tvExpOptDlg>0</tvExpOptDlg>
    <cbSel>0</cbSel>
    <RteFlg>0</RteFlg>
    <File>
      <GroupNumber>1</GroupNumber>
      <FileNumber>1</FileNumber>
      <FileType>5</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\customer_port\sifang\customer_port.h</PathWithFileName>
      <FilenameWithoutPath>customer_port.h</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>1</GroupNumber>
      <FileNumber>2</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\customer_port\sifang\app_can_port.c</PathWithFileName>
      <FilenameWithoutPath>app_can_port.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>1</GroupNumber>
      <FileNumber>3</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\customer_port\sifang\app_rs485_port.c</PathWithFileName>
      <FilenameWithoutPath>app_rs485_port.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>1</GroupNumber>
      <FileNumber>4</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\customer_port\sifang\customer_port.c</PathWithFileName>
      <FilenameWithoutPath>customer_port.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
  </Group>

  <Group>
    <GroupName>user</GroupName>
    <tvExp>1</tvExp>
    <tvExpOptDlg>0</tvExpOptDlg>
    <cbSel>0</cbSel>
    <RteFlg>0</RteFlg>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>5</FileNumber>
      <FileType>5</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\user\inc\uapp.h</PathWithFileName>
      <FilenameWithoutPath>uapp.h</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>6</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\user\app_user.c</PathWithFileName>
      <FilenameWithoutPath>app_user.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>7</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\user\app_sub.c</PathWithFileName>
      <FilenameWithoutPath>app_sub.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>8</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\user\app_main.c</PathWithFileName>
      <FilenameWithoutPath>app_main.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>9</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\user\app_diag.c</PathWithFileName>
      <FilenameWithoutPath>app_diag.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>10</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\user\alg_diag.c</PathWithFileName>
      <FilenameWithoutPath>alg_diag.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>11</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\user\drv_io.c</PathWithFileName>
      <FilenameWithoutPath>drv_io.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>12</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\user\drv_hal_main.c</PathWithFileName>
      <FilenameWithoutPath>drv_hal_main.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>13</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\user\stm32g4xx_hal_msp.c</PathWithFileName>
      <FilenameWithoutPath>stm32g4xx_hal_msp.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>14</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\user\stm32g4xx_it.c</PathWithFileName>
      <FilenameWithoutPath>stm32g4xx_it.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>15</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\user\alg_dio_edge.c</PathWithFileName>
      <FilenameWithoutPath>alg_dio_edge.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>16</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\user\alg_pcmaster.c</PathWithFileName>
      <FilenameWithoutPath>alg_pcmaster.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>17</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\user\alg_toolbox.c</PathWithFileName>
      <FilenameWithoutPath>alg_toolbox.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>18</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\user\alg_uapp.c</PathWithFileName>
      <FilenameWithoutPath>alg_uapp.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>19</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\user\app_cmd.c</PathWithFileName>
      <FilenameWithoutPath>app_cmd.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>20</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\user\app_esp01.c</PathWithFileName>
      <FilenameWithoutPath>app_esp01.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>21</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\user\app_pcmaster.c</PathWithFileName>
      <FilenameWithoutPath>app_pcmaster.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>22</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\user\app_record.c</PathWithFileName>
      <FilenameWithoutPath>app_record.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>23</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\user\app_test.c</PathWithFileName>
      <FilenameWithoutPath>app_test.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>24</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\user\drv_fal_flash_stm32g4_port.c</PathWithFileName>
      <FilenameWithoutPath>drv_fal_flash_stm32g4_port.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>25</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\user\drv_rs485.c</PathWithFileName>
      <FilenameWithoutPath>drv_rs485.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>26</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\user\drv_fdcan.c</PathWithFileName>
      <FilenameWithoutPath>drv_fdcan.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>27</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\user\alg_crc.c</PathWithFileName>
      <FilenameWithoutPath>alg_crc.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>28</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\user\alg_modbus_common.c</PathWithFileName>
      <FilenameWithoutPath>alg_modbus_common.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>29</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\user\app_ad.c</PathWithFileName>
      <FilenameWithoutPath>app_ad.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>30</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\user\alg_ntc_convert.c</PathWithFileName>
      <FilenameWithoutPath>alg_ntc_convert.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>31</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\user\app_alternate_comm.c</PathWithFileName>
      <FilenameWithoutPath>app_alternate_comm.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>32</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\user\drv_soft_iic_hdc1080.c</PathWithFileName>
      <FilenameWithoutPath>drv_soft_iic_hdc1080.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>33</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\user\app_xfebin.c</PathWithFileName>
      <FilenameWithoutPath>app_xfebin.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
  </Group>

  <Group>
    <GroupName>pfc</GroupName>
    <tvExp>0</tvExp>
    <tvExpOptDlg>0</tvExpOptDlg>
    <cbSel>0</cbSel>
    <RteFlg>0</RteFlg>
    <File>
      <GroupNumber>3</GroupNumber>
      <FileNumber>34</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\framework_pfc\app_pfcctrl.c</PathWithFileName>
      <FilenameWithoutPath>app_pfcctrl.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>3</GroupNumber>
      <FileNumber>35</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\framework_pfc\pfc_dqctrl.c</PathWithFileName>
      <FilenameWithoutPath>pfc_dqctrl.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>3</GroupNumber>
      <FileNumber>36</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\framework_pfc\pfc_sdpll.c</PathWithFileName>
      <FilenameWithoutPath>pfc_sdpll.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>3</GroupNumber>
      <FileNumber>37</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\framework_pfc\pfc_isr.c</PathWithFileName>
      <FilenameWithoutPath>pfc_isr.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>3</GroupNumber>
      <FileNumber>38</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\framework_pfc\pfc_pwm.c</PathWithFileName>
      <FilenameWithoutPath>pfc_pwm.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
  </Group>

  <Group>
    <GroupName>motordrv</GroupName>
    <tvExp>0</tvExp>
    <tvExpOptDlg>0</tvExpOptDlg>
    <cbSel>0</cbSel>
    <RteFlg>0</RteFlg>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>39</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\framework_foc\pmsm_inovance\user_mcsdk_task.c</PathWithFileName>
      <FilenameWithoutPath>user_mcsdk_task.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>40</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_abs_f32.c</PathWithFileName>
      <FilenameWithoutPath>arm_abs_f32.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>41</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_abs_q15.c</PathWithFileName>
      <FilenameWithoutPath>arm_abs_q15.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>42</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_common_tables.c</PathWithFileName>
      <FilenameWithoutPath>arm_common_tables.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>43</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_cos_f32.c</PathWithFileName>
      <FilenameWithoutPath>arm_cos_f32.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>44</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_cos_q15.c</PathWithFileName>
      <FilenameWithoutPath>arm_cos_q15.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>45</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_cos_q31.c</PathWithFileName>
      <FilenameWithoutPath>arm_cos_q31.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>46</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_float_to_q15.c</PathWithFileName>
      <FilenameWithoutPath>arm_float_to_q15.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>47</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_q15_to_float.c</PathWithFileName>
      <FilenameWithoutPath>arm_q15_to_float.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>48</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_q15_to_q31.c</PathWithFileName>
      <FilenameWithoutPath>arm_q15_to_q31.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>49</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_q31_to_q15.c</PathWithFileName>
      <FilenameWithoutPath>arm_q31_to_q15.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>50</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_sin_cos_f32.c</PathWithFileName>
      <FilenameWithoutPath>arm_sin_cos_f32.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>51</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_sin_cos_q31.c</PathWithFileName>
      <FilenameWithoutPath>arm_sin_cos_q31.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>52</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_sin_f32.c</PathWithFileName>
      <FilenameWithoutPath>arm_sin_f32.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>53</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_sin_q15.c</PathWithFileName>
      <FilenameWithoutPath>arm_sin_q15.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>54</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_sin_q31.c</PathWithFileName>
      <FilenameWithoutPath>arm_sin_q31.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>55</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_sqrt_q15.c</PathWithFileName>
      <FilenameWithoutPath>arm_sqrt_q15.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>56</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_sqrt_q31.c</PathWithFileName>
      <FilenameWithoutPath>arm_sqrt_q31.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>57</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\framework_foc\pmsm_inovance\Core\Src\userApps\DeviceInit.c</PathWithFileName>
      <FilenameWithoutPath>DeviceInit.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>58</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\framework_foc\pmsm_inovance\Core\Src\userApps\f_comm.c</PathWithFileName>
      <FilenameWithoutPath>f_comm.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>59</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\framework_foc\pmsm_inovance\Core\Src\userApps\f_error.c</PathWithFileName>
      <FilenameWithoutPath>f_error.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>60</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\framework_foc\pmsm_inovance\Core\Src\userApps\f_frqSrc.c</PathWithFileName>
      <FilenameWithoutPath>f_frqSrc.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>61</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\framework_foc\pmsm_inovance\Core\Src\userApps\f_funcCode.c</PathWithFileName>
      <FilenameWithoutPath>f_funcCode.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>62</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\framework_foc\pmsm_inovance\Core\Src\userApps\f_interface.c</PathWithFileName>
      <FilenameWithoutPath>f_interface.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>63</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\framework_foc\pmsm_inovance\Core\Src\userApps\f_main.c</PathWithFileName>
      <FilenameWithoutPath>f_main.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>64</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\framework_foc\pmsm_inovance\Core\Src\userApps\f_runSrc.c</PathWithFileName>
      <FilenameWithoutPath>f_runSrc.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>65</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\framework_foc\pmsm_inovance\Core\Src\userApps\f_runSrc_accDecFrq.c</PathWithFileName>
      <FilenameWithoutPath>f_runSrc_accDecFrq.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>66</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorCarrier.c</PathWithFileName>
      <FilenameWithoutPath>MotorCarrier.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>67</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorConst.c</PathWithFileName>
      <FilenameWithoutPath>MotorConst.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>68</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorCurrentTransform.c</PathWithFileName>
      <FilenameWithoutPath>MotorCurrentTransform.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>69</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorDataExchange.c</PathWithFileName>
      <FilenameWithoutPath>MotorDataExchange.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>70</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorEncoder.c</PathWithFileName>
      <FilenameWithoutPath>MotorEncoder.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>71</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorFlyingStart.c</PathWithFileName>
      <FilenameWithoutPath>MotorFlyingStart.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>72</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorInfoCollect.c</PathWithFileName>
      <FilenameWithoutPath>MotorInfoCollect.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>73</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorInvProtect.c</PathWithFileName>
      <FilenameWithoutPath>MotorInvProtect.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>74</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorIPMSVC.c</PathWithFileName>
      <FilenameWithoutPath>MotorIPMSVC.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>75</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorMain.c</PathWithFileName>
      <FilenameWithoutPath>MotorMain.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>76</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorParChange.c</PathWithFileName>
      <FilenameWithoutPath>MotorParChange.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>77</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorParEst.c</PathWithFileName>
      <FilenameWithoutPath>MotorParEst.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>78</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorPmsmMain.c</PathWithFileName>
      <FilenameWithoutPath>MotorPmsmMain.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>79</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorPmsmParEst.c</PathWithFileName>
      <FilenameWithoutPath>MotorPmsmParEst.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>80</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorPublicCal.c</PathWithFileName>
      <FilenameWithoutPath>MotorPublicCal.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>81</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorPWM.c</PathWithFileName>
      <FilenameWithoutPath>MotorPWM.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>82</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorSpeed.c</PathWithFileName>
      <FilenameWithoutPath>MotorSpeed.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>83</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorVar.c</PathWithFileName>
      <FilenameWithoutPath>MotorVar.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>84</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorVCMain.c</PathWithFileName>
      <FilenameWithoutPath>MotorVCMain.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>85</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorVF.c</PathWithFileName>
      <FilenameWithoutPath>MotorVF.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>86</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\framework_foc\pmsm_inovance\Core\Src\userApps\Scope.c</PathWithFileName>
      <FilenameWithoutPath>Scope.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>87</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\framework_foc\pmsm_inovance\Core\Src\userApps\SubPrg.c</PathWithFileName>
      <FilenameWithoutPath>SubPrg.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>88</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\framework_foc\pmsm_inovance\Core\Src\userApps\Svpwm.c</PathWithFileName>
      <FilenameWithoutPath>Svpwm.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>89</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\framework_foc\pmsm_inovance\Core\Src\userApps\testApp.c</PathWithFileName>
      <FilenameWithoutPath>testApp.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
  </Group>

  <Group>
    <GroupName>protocol_comm</GroupName>
    <tvExp>1</tvExp>
    <tvExpOptDlg>0</tvExpOptDlg>
    <cbSel>0</cbSel>
    <RteFlg>0</RteFlg>
    <File>
      <GroupNumber>5</GroupNumber>
      <FileNumber>90</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\bsmcomm_protocol\app_bsm_com_outernet.c</PathWithFileName>
      <FilenameWithoutPath>app_bsm_com_outernet.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>5</GroupNumber>
      <FileNumber>91</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\bsmcomm_protocol\api_bsm_com_protocol.c</PathWithFileName>
      <FilenameWithoutPath>api_bsm_com_protocol.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
  </Group>

  <Group>
    <GroupName>Drivers</GroupName>
    <tvExp>0</tvExp>
    <tvExpOptDlg>0</tvExpOptDlg>
    <cbSel>0</cbSel>
    <RteFlg>0</RteFlg>
    <File>
      <GroupNumber>6</GroupNumber>
      <FileNumber>92</FileNumber>
      <FileType>5</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\rtconfig.h</PathWithFileName>
      <FilenameWithoutPath>rtconfig.h</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>6</GroupNumber>
      <FileNumber>93</FileNumber>
      <FileType>2</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\libraries\STM32G4xx_HAL\CMSIS\Device\ST\STM32G4xx\Source\Templates\arm\startup_stm32g474xx.s</PathWithFileName>
      <FilenameWithoutPath>startup_stm32g474xx.s</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>6</GroupNumber>
      <FileNumber>94</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>board\board.c</PathWithFileName>
      <FilenameWithoutPath>board.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>6</GroupNumber>
      <FileNumber>95</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\libraries\HAL_Drivers\drv_gpio.c</PathWithFileName>
      <FilenameWithoutPath>drv_gpio.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>6</GroupNumber>
      <FileNumber>96</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\libraries\HAL_Drivers\drv_spi.c</PathWithFileName>
      <FilenameWithoutPath>drv_spi.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>6</GroupNumber>
      <FileNumber>97</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\libraries\HAL_Drivers\drv_crypto.c</PathWithFileName>
      <FilenameWithoutPath>drv_crypto.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>6</GroupNumber>
      <FileNumber>98</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\libraries\HAL_Drivers\drv_wdt.c</PathWithFileName>
      <FilenameWithoutPath>drv_wdt.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>6</GroupNumber>
      <FileNumber>99</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\libraries\HAL_Drivers\drv_usart.c</PathWithFileName>
      <FilenameWithoutPath>drv_usart.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>6</GroupNumber>
      <FileNumber>100</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\libraries\HAL_Drivers\drv_common.c</PathWithFileName>
      <FilenameWithoutPath>drv_common.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>6</GroupNumber>
      <FileNumber>101</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\libraries\HAL_Drivers\drv_rtc.c</PathWithFileName>
      <FilenameWithoutPath>drv_rtc.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
  </Group>

  <Group>
    <GroupName>Applications</GroupName>
    <tvExp>0</tvExp>
    <tvExpOptDlg>0</tvExpOptDlg>
    <cbSel>0</cbSel>
    <RteFlg>0</RteFlg>
    <File>
      <GroupNumber>7</GroupNumber>
      <FileNumber>102</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>applications\main.c</PathWithFileName>
      <FilenameWithoutPath>main.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
  </Group>

  <Group>
    <GroupName>CPU</GroupName>
    <tvExp>0</tvExp>
    <tvExpOptDlg>0</tvExpOptDlg>
    <cbSel>0</cbSel>
    <RteFlg>0</RteFlg>
    <File>
      <GroupNumber>8</GroupNumber>
      <FileNumber>103</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\..\libcpu\arm\common\div0.c</PathWithFileName>
      <FilenameWithoutPath>div0.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>8</GroupNumber>
      <FileNumber>104</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\..\libcpu\arm\common\backtrace.c</PathWithFileName>
      <FilenameWithoutPath>backtrace.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>8</GroupNumber>
      <FileNumber>105</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\..\libcpu\arm\common\showmem.c</PathWithFileName>
      <FilenameWithoutPath>showmem.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>8</GroupNumber>
      <FileNumber>106</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\..\libcpu\arm\cortex-m4\cpuport.c</PathWithFileName>
      <FilenameWithoutPath>cpuport.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>8</GroupNumber>
      <FileNumber>107</FileNumber>
      <FileType>2</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\..\libcpu\arm\cortex-m4\context_rvds.S</PathWithFileName>
      <FilenameWithoutPath>context_rvds.S</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
  </Group>

  <Group>
    <GroupName>DeviceDrivers</GroupName>
    <tvExp>0</tvExp>
    <tvExpOptDlg>0</tvExpOptDlg>
    <cbSel>0</cbSel>
    <RteFlg>0</RteFlg>
    <File>
      <GroupNumber>9</GroupNumber>
      <FileNumber>108</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\..\components\drivers\hwcrypto\hwcrypto.c</PathWithFileName>
      <FilenameWithoutPath>hwcrypto.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>9</GroupNumber>
      <FileNumber>109</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\..\components\drivers\misc\pin.c</PathWithFileName>
      <FilenameWithoutPath>pin.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>9</GroupNumber>
      <FileNumber>110</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\..\components\drivers\mtd\mtd_nor.c</PathWithFileName>
      <FilenameWithoutPath>mtd_nor.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>9</GroupNumber>
      <FileNumber>111</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\..\components\drivers\rtc\soft_rtc.c</PathWithFileName>
      <FilenameWithoutPath>soft_rtc.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>9</GroupNumber>
      <FileNumber>112</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\..\components\drivers\rtc\rtc.c</PathWithFileName>
      <FilenameWithoutPath>rtc.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>9</GroupNumber>
      <FileNumber>113</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\..\components\drivers\serial\serial.c</PathWithFileName>
      <FilenameWithoutPath>serial.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>9</GroupNumber>
      <FileNumber>114</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\..\components\drivers\spi\sfud\src\sfud.c</PathWithFileName>
      <FilenameWithoutPath>sfud.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>9</GroupNumber>
      <FileNumber>115</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\..\components\drivers\spi\spi_flash_sfud.c</PathWithFileName>
      <FilenameWithoutPath>spi_flash_sfud.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>9</GroupNumber>
      <FileNumber>116</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\..\components\drivers\spi\sfud\src\sfud_sfdp.c</PathWithFileName>
      <FilenameWithoutPath>sfud_sfdp.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>9</GroupNumber>
      <FileNumber>117</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\..\components\drivers\spi\spi_core.c</PathWithFileName>
      <FilenameWithoutPath>spi_core.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>9</GroupNumber>
      <FileNumber>118</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\..\components\drivers\spi\spi_dev.c</PathWithFileName>
      <FilenameWithoutPath>spi_dev.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>9</GroupNumber>
      <FileNumber>119</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\..\components\drivers\src\workqueue.c</PathWithFileName>
      <FilenameWithoutPath>workqueue.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>9</GroupNumber>
      <FileNumber>120</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\..\components\drivers\src\completion.c</PathWithFileName>
      <FilenameWithoutPath>completion.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>9</GroupNumber>
      <FileNumber>121</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\..\components\drivers\src\dataqueue.c</PathWithFileName>
      <FilenameWithoutPath>dataqueue.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>9</GroupNumber>
      <FileNumber>122</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\..\components\drivers\src\pipe.c</PathWithFileName>
      <FilenameWithoutPath>pipe.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>9</GroupNumber>
      <FileNumber>123</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\..\components\drivers\src\ringblk_buf.c</PathWithFileName>
      <FilenameWithoutPath>ringblk_buf.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>9</GroupNumber>
      <FileNumber>124</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\..\components\drivers\src\waitqueue.c</PathWithFileName>
      <FilenameWithoutPath>waitqueue.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>9</GroupNumber>
      <FileNumber>125</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\..\components\drivers\src\ringbuffer.c</PathWithFileName>
      <FilenameWithoutPath>ringbuffer.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>9</GroupNumber>
      <FileNumber>126</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\..\components\drivers\watchdog\watchdog.c</PathWithFileName>
      <FilenameWithoutPath>watchdog.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
  </Group>

  <Group>
    <GroupName>fal</GroupName>
    <tvExp>1</tvExp>
    <tvExpOptDlg>0</tvExpOptDlg>
    <cbSel>0</cbSel>
    <RteFlg>0</RteFlg>
    <File>
      <GroupNumber>10</GroupNumber>
      <FileNumber>127</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>packages\fal-v0.5.0\src\fal_rtt.c</PathWithFileName>
      <FilenameWithoutPath>fal_rtt.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>10</GroupNumber>
      <FileNumber>128</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>packages\fal-v0.5.0\src\fal.c</PathWithFileName>
      <FilenameWithoutPath>fal.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>10</GroupNumber>
      <FileNumber>129</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>packages\fal-v0.5.0\samples\porting\fal_flash_sfud_port.c</PathWithFileName>
      <FilenameWithoutPath>fal_flash_sfud_port.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>10</GroupNumber>
      <FileNumber>130</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>packages\fal-v0.5.0\src\fal_partition.c</PathWithFileName>
      <FilenameWithoutPath>fal_partition.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>10</GroupNumber>
      <FileNumber>131</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>packages\fal-v0.5.0\src\fal_flash.c</PathWithFileName>
      <FilenameWithoutPath>fal_flash.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
  </Group>

  <Group>
    <GroupName>Filesystem</GroupName>
    <tvExp>0</tvExp>
    <tvExpOptDlg>0</tvExpOptDlg>
    <cbSel>0</cbSel>
    <RteFlg>0</RteFlg>
    <File>
      <GroupNumber>11</GroupNumber>
      <FileNumber>132</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\..\components\dfs\src\dfs_posix.c</PathWithFileName>
      <FilenameWithoutPath>dfs_posix.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>11</GroupNumber>
      <FileNumber>133</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\..\components\dfs\src\dfs_fs.c</PathWithFileName>
      <FilenameWithoutPath>dfs_fs.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>11</GroupNumber>
      <FileNumber>134</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\..\components\dfs\src\dfs.c</PathWithFileName>
      <FilenameWithoutPath>dfs.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>11</GroupNumber>
      <FileNumber>135</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\..\components\dfs\src\dfs_file.c</PathWithFileName>
      <FilenameWithoutPath>dfs_file.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>11</GroupNumber>
      <FileNumber>136</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\..\components\dfs\filesystems\devfs\devfs.c</PathWithFileName>
      <FilenameWithoutPath>devfs.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
  </Group>

  <Group>
    <GroupName>Finsh</GroupName>
    <tvExp>0</tvExp>
    <tvExpOptDlg>0</tvExpOptDlg>
    <cbSel>0</cbSel>
    <RteFlg>0</RteFlg>
    <File>
      <GroupNumber>12</GroupNumber>
      <FileNumber>137</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\..\components\finsh\shell.c</PathWithFileName>
      <FilenameWithoutPath>shell.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>12</GroupNumber>
      <FileNumber>138</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\..\components\finsh\msh.c</PathWithFileName>
      <FilenameWithoutPath>msh.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>12</GroupNumber>
      <FileNumber>139</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\..\components\finsh\msh_file.c</PathWithFileName>
      <FilenameWithoutPath>msh_file.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>12</GroupNumber>
      <FileNumber>140</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\..\components\finsh\cmd.c</PathWithFileName>
      <FilenameWithoutPath>cmd.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
  </Group>

  <Group>
    <GroupName>Kernel</GroupName>
    <tvExp>0</tvExp>
    <tvExpOptDlg>0</tvExpOptDlg>
    <cbSel>0</cbSel>
    <RteFlg>0</RteFlg>
    <File>
      <GroupNumber>13</GroupNumber>
      <FileNumber>141</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\..\src\mem.c</PathWithFileName>
      <FilenameWithoutPath>mem.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>13</GroupNumber>
      <FileNumber>142</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\..\src\scheduler.c</PathWithFileName>
      <FilenameWithoutPath>scheduler.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>13</GroupNumber>
      <FileNumber>143</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\..\src\idle.c</PathWithFileName>
      <FilenameWithoutPath>idle.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>13</GroupNumber>
      <FileNumber>144</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\..\src\timer.c</PathWithFileName>
      <FilenameWithoutPath>timer.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>13</GroupNumber>
      <FileNumber>145</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\..\src\thread.c</PathWithFileName>
      <FilenameWithoutPath>thread.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>13</GroupNumber>
      <FileNumber>146</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\..\src\device.c</PathWithFileName>
      <FilenameWithoutPath>device.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>13</GroupNumber>
      <FileNumber>147</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\..\src\mempool.c</PathWithFileName>
      <FilenameWithoutPath>mempool.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>13</GroupNumber>
      <FileNumber>148</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\..\src\irq.c</PathWithFileName>
      <FilenameWithoutPath>irq.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>13</GroupNumber>
      <FileNumber>149</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\..\src\kservice.c</PathWithFileName>
      <FilenameWithoutPath>kservice.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>13</GroupNumber>
      <FileNumber>150</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\..\src\clock.c</PathWithFileName>
      <FilenameWithoutPath>clock.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>13</GroupNumber>
      <FileNumber>151</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\..\src\object.c</PathWithFileName>
      <FilenameWithoutPath>object.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>13</GroupNumber>
      <FileNumber>152</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\..\src\components.c</PathWithFileName>
      <FilenameWithoutPath>components.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>13</GroupNumber>
      <FileNumber>153</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\..\src\ipc.c</PathWithFileName>
      <FilenameWithoutPath>ipc.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
  </Group>

  <Group>
    <GroupName>libc</GroupName>
    <tvExp>0</tvExp>
    <tvExpOptDlg>0</tvExpOptDlg>
    <cbSel>0</cbSel>
    <RteFlg>0</RteFlg>
    <File>
      <GroupNumber>14</GroupNumber>
      <FileNumber>154</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\..\components\libc\compilers\common\time.c</PathWithFileName>
      <FilenameWithoutPath>time.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
  </Group>

  <Group>
    <GroupName>Libraries</GroupName>
    <tvExp>0</tvExp>
    <tvExpOptDlg>0</tvExpOptDlg>
    <cbSel>0</cbSel>
    <RteFlg>0</RteFlg>
    <File>
      <GroupNumber>15</GroupNumber>
      <FileNumber>155</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_dma_ex.c</PathWithFileName>
      <FilenameWithoutPath>stm32g4xx_hal_dma_ex.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>15</GroupNumber>
      <FileNumber>156</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_gpio.c</PathWithFileName>
      <FilenameWithoutPath>stm32g4xx_hal_gpio.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>15</GroupNumber>
      <FileNumber>157</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_rcc_ex.c</PathWithFileName>
      <FilenameWithoutPath>stm32g4xx_hal_rcc_ex.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>15</GroupNumber>
      <FileNumber>158</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_iwdg.c</PathWithFileName>
      <FilenameWithoutPath>stm32g4xx_hal_iwdg.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>15</GroupNumber>
      <FileNumber>159</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_adc_ex.c</PathWithFileName>
      <FilenameWithoutPath>stm32g4xx_hal_adc_ex.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>15</GroupNumber>
      <FileNumber>160</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_rtc.c</PathWithFileName>
      <FilenameWithoutPath>stm32g4xx_hal_rtc.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>15</GroupNumber>
      <FileNumber>161</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_uart_ex.c</PathWithFileName>
      <FilenameWithoutPath>stm32g4xx_hal_uart_ex.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>15</GroupNumber>
      <FileNumber>162</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_rtc_ex.c</PathWithFileName>
      <FilenameWithoutPath>stm32g4xx_hal_rtc_ex.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>15</GroupNumber>
      <FileNumber>163</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_tim.c</PathWithFileName>
      <FilenameWithoutPath>stm32g4xx_hal_tim.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>15</GroupNumber>
      <FileNumber>164</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_cryp_ex.c</PathWithFileName>
      <FilenameWithoutPath>stm32g4xx_hal_cryp_ex.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>15</GroupNumber>
      <FileNumber>165</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_pwr.c</PathWithFileName>
      <FilenameWithoutPath>stm32g4xx_hal_pwr.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>15</GroupNumber>
      <FileNumber>166</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_dac_ex.c</PathWithFileName>
      <FilenameWithoutPath>stm32g4xx_hal_dac_ex.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>15</GroupNumber>
      <FileNumber>167</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_flash_ex.c</PathWithFileName>
      <FilenameWithoutPath>stm32g4xx_hal_flash_ex.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>15</GroupNumber>
      <FileNumber>168</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_fdcan.c</PathWithFileName>
      <FilenameWithoutPath>stm32g4xx_hal_fdcan.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>15</GroupNumber>
      <FileNumber>169</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_cryp.c</PathWithFileName>
      <FilenameWithoutPath>stm32g4xx_hal_cryp.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>15</GroupNumber>
      <FileNumber>170</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_usart_ex.c</PathWithFileName>
      <FilenameWithoutPath>stm32g4xx_hal_usart_ex.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>15</GroupNumber>
      <FileNumber>171</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_dac.c</PathWithFileName>
      <FilenameWithoutPath>stm32g4xx_hal_dac.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>15</GroupNumber>
      <FileNumber>172</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_rng.c</PathWithFileName>
      <FilenameWithoutPath>stm32g4xx_hal_rng.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>15</GroupNumber>
      <FileNumber>173</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_tim_ex.c</PathWithFileName>
      <FilenameWithoutPath>stm32g4xx_hal_tim_ex.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>15</GroupNumber>
      <FileNumber>174</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_qspi.c</PathWithFileName>
      <FilenameWithoutPath>stm32g4xx_hal_qspi.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>15</GroupNumber>
      <FileNumber>175</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_wwdg.c</PathWithFileName>
      <FilenameWithoutPath>stm32g4xx_hal_wwdg.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>15</GroupNumber>
      <FileNumber>176</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_usart.c</PathWithFileName>
      <FilenameWithoutPath>stm32g4xx_hal_usart.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>15</GroupNumber>
      <FileNumber>177</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_cortex.c</PathWithFileName>
      <FilenameWithoutPath>stm32g4xx_hal_cortex.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>15</GroupNumber>
      <FileNumber>178</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_rcc.c</PathWithFileName>
      <FilenameWithoutPath>stm32g4xx_hal_rcc.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>15</GroupNumber>
      <FileNumber>179</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_dma.c</PathWithFileName>
      <FilenameWithoutPath>stm32g4xx_hal_dma.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>15</GroupNumber>
      <FileNumber>180</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_uart.c</PathWithFileName>
      <FilenameWithoutPath>stm32g4xx_hal_uart.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>15</GroupNumber>
      <FileNumber>181</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_flash.c</PathWithFileName>
      <FilenameWithoutPath>stm32g4xx_hal_flash.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>15</GroupNumber>
      <FileNumber>182</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_pwr_ex.c</PathWithFileName>
      <FilenameWithoutPath>stm32g4xx_hal_pwr_ex.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>15</GroupNumber>
      <FileNumber>183</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\libraries\STM32G4xx_HAL\CMSIS\Device\ST\STM32G4xx\Source\Templates\system_stm32g4xx.c</PathWithFileName>
      <FilenameWithoutPath>system_stm32g4xx.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>15</GroupNumber>
      <FileNumber>184</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_spi.c</PathWithFileName>
      <FilenameWithoutPath>stm32g4xx_hal_spi.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>15</GroupNumber>
      <FileNumber>185</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_adc.c</PathWithFileName>
      <FilenameWithoutPath>stm32g4xx_hal_adc.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>15</GroupNumber>
      <FileNumber>186</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_nor.c</PathWithFileName>
      <FilenameWithoutPath>stm32g4xx_hal_nor.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>15</GroupNumber>
      <FileNumber>187</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal.c</PathWithFileName>
      <FilenameWithoutPath>stm32g4xx_hal.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>15</GroupNumber>
      <FileNumber>188</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_crc.c</PathWithFileName>
      <FilenameWithoutPath>stm32g4xx_hal_crc.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>15</GroupNumber>
      <FileNumber>189</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_cordic.c</PathWithFileName>
      <FilenameWithoutPath>stm32g4xx_hal_cordic.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>15</GroupNumber>
      <FileNumber>190</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_ll_cordic.c</PathWithFileName>
      <FilenameWithoutPath>stm32g4xx_ll_cordic.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>15</GroupNumber>
      <FileNumber>191</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\..\..\..\DPS\vfd_3phase_pfc\bsp\stm32\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_hrtim.c</PathWithFileName>
      <FilenameWithoutPath>stm32g4xx_hal_hrtim.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
  </Group>

  <Group>
    <GroupName>littlefs</GroupName>
    <tvExp>0</tvExp>
    <tvExpOptDlg>0</tvExpOptDlg>
    <cbSel>0</cbSel>
    <RteFlg>0</RteFlg>
    <File>
      <GroupNumber>16</GroupNumber>
      <FileNumber>192</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>packages\littlefs-latest\dfs_lfs.c</PathWithFileName>
      <FilenameWithoutPath>dfs_lfs.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>16</GroupNumber>
      <FileNumber>193</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>packages\littlefs-latest\lfs_util.c</PathWithFileName>
      <FilenameWithoutPath>lfs_util.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>16</GroupNumber>
      <FileNumber>194</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>packages\littlefs-latest\lfs.c</PathWithFileName>
      <FilenameWithoutPath>lfs.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>16</GroupNumber>
      <FileNumber>195</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>packages\littlefs-latest\lfs_crc.c</PathWithFileName>
      <FilenameWithoutPath>lfs_crc.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
  </Group>

  <Group>
    <GroupName>Utilities</GroupName>
    <tvExp>0</tvExp>
    <tvExpOptDlg>0</tvExpOptDlg>
    <cbSel>0</cbSel>
    <RteFlg>0</RteFlg>
    <File>
      <GroupNumber>17</GroupNumber>
      <FileNumber>196</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\..\components\utilities\ulog\ulog.c</PathWithFileName>
      <FilenameWithoutPath>ulog.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>17</GroupNumber>
      <FileNumber>197</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\..\components\utilities\ulog\backend\console_be.c</PathWithFileName>
      <FilenameWithoutPath>console_be.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
  </Group>

  <Group>
    <GroupName>FlashDB</GroupName>
    <tvExp>0</tvExp>
    <tvExpOptDlg>0</tvExpOptDlg>
    <cbSel>0</cbSel>
    <RteFlg>0</RteFlg>
    <File>
      <GroupNumber>18</GroupNumber>
      <FileNumber>198</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\packages\FlashDB-v1.1.0\src\fdb_cmd.c</PathWithFileName>
      <FilenameWithoutPath>fdb_cmd.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>18</GroupNumber>
      <FileNumber>199</FileNumber>
      <FileType>1</FileType>
      <tvExp>1</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\packages\FlashDB-v1.1.0\src\fdb.c</PathWithFileName>
      <FilenameWithoutPath>fdb.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>18</GroupNumber>
      <FileNumber>200</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\packages\FlashDB-v1.1.0\src\fdb_kvdb.c</PathWithFileName>
      <FilenameWithoutPath>fdb_kvdb.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>18</GroupNumber>
      <FileNumber>201</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\packages\FlashDB-v1.1.0\src\fdb_tsdb.c</PathWithFileName>
      <FilenameWithoutPath>fdb_tsdb.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>18</GroupNumber>
      <FileNumber>202</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\packages\FlashDB-v1.1.0\src\fdb_utils.c</PathWithFileName>
      <FilenameWithoutPath>fdb_utils.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>18</GroupNumber>
      <FileNumber>203</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\packages\FlashDB-v1.1.0\samples\kvdb_basic_sample.c</PathWithFileName>
      <FilenameWithoutPath>kvdb_basic_sample.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>18</GroupNumber>
      <FileNumber>204</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>.\packages\FlashDB-v1.1.0\samples\tsdb_sample.c</PathWithFileName>
      <FilenameWithoutPath>tsdb_sample.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
  </Group>

</ProjectOpt>
