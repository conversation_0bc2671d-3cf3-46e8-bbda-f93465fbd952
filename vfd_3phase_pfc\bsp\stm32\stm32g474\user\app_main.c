
/******************** (C) COPYRIGHT 2021     ***********************************
* File Name          : app_main.c
* Author             : xiou
* Version            : V1.0
* Date               :
* Description        : this is vfd system main entry
                        * create the task
                        * create the main/sub entry for task
                        * entry should show the brief of system architecture ,
                          do not include the details
                        * function call by entry should be achieved in *_uapp.c/*_sub.c
********************************************************************************/
#include <rtthread.h>
#include <rtdevice.h>
#include <board.h>
#include "uapp.h"

#define DBG_TAG "main"
#define DBG_LVL DBG_LOG
#include <rtdbg.h>

/* Private variables ------------------------------------------------------------*/
static uint8_t sem_init = 0;
static struct rt_semaphore sem_logic_1ms = {0};
static struct rt_semaphore sem_sub1_1ms = {0};
static struct rt_semaphore sem_sub2_1ms = {0};
/* Private variables end---------------------------------------------------------*/

/* Private function prototypes --------------------------------------------------*/
void App_McCtrl_Init(void);
void App_McCtrl_Task(void);
/* Private function prototypes end-----------------------------------------------*/
extern void nvsdata_write_task(void);
extern void inverter_pwmctr(void);
extern void adc_sample_task(void);
extern void rt_wdt_feedog(void);
extern void rt_wdt_set_timeout(uint8_t sec);
extern void system_diagnostic_logic(void);

void vfd_main_entry(void *parameter)
{
    static uint32_t main_cnt = 0;
    vfd.log_size = (uint32_t)&vfd.diag_stop_times - (uint32_t)&vfd.date + sizeof(vfd.diag_stop_times);
    
    /* HAL init */
    vfd_init();
    #if(BOARD_USE_WATCH_DOG)
    rt_wdt_feedog();
    #endif
    
    rt_thread_delay(3*RT_TICK_PER_SECOND);
    
    #if(BOARD_USE_WATCH_DOG)
    rt_wdt_set_timeout(2);
    rt_wdt_feedog();
    #endif
    
    rt_memset(&vfd.diag,0,sizeof(&vfd.diag));
    
    /* data init */
    while (1)
    {
        rt_sem_take(&sem_logic_1ms, RT_WAITING_FOREVER);
        sem_logic_1ms.value = 0;

        main_cnt++;
        
        #if(BOARD_USE_WATCH_DOG)
        rt_wdt_feedog();
        #endif
        
        adc_sample_task();
        
        if(main_cnt % 10 == 0)/*!< 10ms */
            vfd_io_control();
        
        if((vfd.bit.nvs_datas_init == 0) && (rt_tick_get() <= 60*RT_TICK_PER_SECOND))
            continue;
        else if(!vfd.bit.system_init)
        {
            main_cnt = 0;
            rt_kprintf(" app_main/vfd_main_entry/nvs_datas_init tick [%d] \r\n",rt_tick_get());
        }
        
        if(main_cnt % 1 == 0)/*!< 1ms */
        {
            vfd.pfc->ops->machine_task();
        }
        
        if(main_cnt % 10 == 0)/*!< 10ms */
        {
            system_diagnostic_logic();
            
            vfd_machine_task();             /*!< vfd system state machine */
        }
        
        if(main_cnt % 5 == 0)/*!< 5ms */
        {
            vfd.inverter->ops->machine_task();/*!< inverter svpwm system state machine */
        }
        
        if(!vfd.bit.system_init)
        {
            vfd.bit.system_init = 1;
            rt_kprintf(" app_main/vfd_main_entry/system_init tick [%d] \r\n",rt_tick_get());
        }
    }
}
extern TIM_HandleTypeDef htim8;
extern TIM_HandleTypeDef htim1;
extern void vfd_test_trigle_diag_record(void);
extern void update_local_time_data(void);
extern int led_control_logic(void);
extern void FastPrintf_ToSerialPlot(void);
extern void TIMx_PWM_Test(TIM_HandleTypeDef *htim, uint16_t dutyu,uint16_t dutyv,uint16_t dutyw);

void vfd_sub1_entry(void *parameter)
{
    static uint32_t sub1_cnt = 0;
    
    rt_thread_delay(RT_TICK_PER_SECOND/2);
    vfd_sub_init();
    
    while (1)
    {
        rt_sem_take(&sem_sub1_1ms, RT_WAITING_FOREVER);
        sem_sub1_1ms.value = 0;
        sub1_cnt++;
        
        {
            void Test_IN1_Autoswitch(uint32_t div);
//            Test_IN1_Autoswitch(20000);
        }
        
        if(sub1_cnt % 5 == 0)
            FastPrintf_ToSerialPlot();
           
        if(sub1_cnt % 250 == 0) /*!< 250ms */
        {
            update_local_time_data();
        }
        
        if(sub1_cnt % 10 == 0) /*!< 10ms */
        {
            vfd_test_trigle_diag_record();
            dio_edge_update_all();
            com_cnters_ticking();
            led_control_logic();
            dio_table_read();
            
            TIMx_PWM_Test(&htim1,vfd.manual.tim1_u_duty,vfd.manual.tim1_v_duty,vfd.manual.tim1_w_duty);
            TIMx_PWM_Test(&htim8,vfd.manual.tim8_u_duty,vfd.manual.tim8_v_duty,vfd.manual.tim8_w_duty);
        }
    }
}

extern void record_logdata_init(void);
extern void record_logdata_cyclicity_write(void);
extern int rt_flash_filesystem_init(void);
extern int fdb_init_sample(void);
extern int vfd_operation_statistics(void);
extern int nvs_erase_allchip(void);
extern uint8_t rcc_reset_code;
void vfd_sub2_entry(void *parameter)
{
    static uint8_t flag = 0;
    static uint32_t sub2_cnt = 0;
    uint16_t log_div = 1000;
    
    record_logdata_init();
    rt_flash_filesystem_init();
    fdb_init_sample();
    record_logdata_push(LOG_PowerOn,rcc_reset_code);
    
    while (1)
    {
        rt_sem_take(&sem_sub2_1ms, RT_WAITING_FOREVER);
        sem_sub2_1ms.value = 0;
        sub2_cnt++;
        
        /* wait for nvs init ok*/
        if(flag == 0)
        {
            int result;
            result = nvs_datas_init();
            if(result == 0)
            {          
                ReadSetInfo(); 
                flag = 1;                
            }
        }
        /* com/record/ */
        if(sub2_cnt % 400 == 0) /*!< 400ms */
        {
            record_logdata_cyclicity_write();   
        }
        
        if(rt_tick_get() <= (10*RT_TICK_PER_SECOND))
        {
            continue;
        }
        
        static uint16_t cycle_1s_delay = 0;
        static uint8_t  prev_st = 0;
        
        if((prev_st != vfd.ctrl.sys_st) && (vfd.ctrl.sys_st == ST_RUN))
        {
            cycle_1s_delay = 10000;
        }
        prev_st = vfd.ctrl.sys_st;
        
        if(cycle_1s_delay > 0)
        {
            cycle_1s_delay--;
            log_div = 1000; // 1s
        }
		else if(vfd.ctrl.sys_st == ST_RUN)
            log_div = 10000; // 10s
        else
            log_div = 30000; // 30s

        static uint32_t prev_off = 0;
        
        if((rt_pin_read(POW_PIN) == 0) && (vfd.bit.pow_init) 
            && ((rt_tick_get() - prev_off) > RT_TICK_PER_SECOND))
        {
            prev_off = rt_tick_get();
            record_logdata_push(LOG_PowerOff,0);
        } 
        
        if(sub2_cnt % log_div == 0)/*!< 10 * div ms */
        {
            record_logdata_push(LOG_Idle,0);
        }
        
        if(sub2_cnt % 10 == 0) /*!< 10ms */
        {
            vfd_operation_statistics();
        
            if(vfd.bit.nvs_datas_init)
                nvsdata_write_task();
        
            if(vfd.ctrl.erase_flash)
            {
                nvs_erase_allchip();
            }
            else
            {
                record_pop_appendtofdb();
            }
        }
        
        
    }
}

void vfd_1ms_release_sem(void)
{
    static uint32_t cnt = 0;

    // 500us isr RT_TICK_EPR_SECON 2000
    if (sem_init)
    {
        cnt++;

        if (cnt % 2 == 0)
        {
            rt_sem_release(&sem_logic_1ms);
            rt_sem_release(&sem_sub1_1ms);
            rt_sem_release(&sem_sub2_1ms);
        }
    }
}


int rs485_thread_create(const char *name,
                        rt_uint32_t stack_size,
                        rt_uint8_t  priority,
                        rt_uint32_t tick);

int vfd_create_thread(void)
{
    rt_thread_t tid;

    rt_sem_init(&sem_logic_1ms, "logic_10ms", 0, RT_IPC_FLAG_FIFO);
    rt_sem_init(&sem_sub1_1ms,  "sub1_10ms", 0, RT_IPC_FLAG_FIFO);
    rt_sem_init(&sem_sub2_1ms,  "sub2_10ms", 0, RT_IPC_FLAG_FIFO);

    sem_init = 1;

    tid = rt_thread_create("logic", vfd_main_entry, RT_NULL, 2048, 1, 20);
    if (tid != NULL)   rt_thread_startup(tid);

    tid = rt_thread_create("sub1", vfd_sub1_entry, RT_NULL, 1024, 10, 10);
    if (tid != NULL)   rt_thread_startup(tid);

    tid = rt_thread_create("sub2", vfd_sub2_entry, RT_NULL, 2048, 11, 10);
    if (tid != NULL)   rt_thread_startup(tid);

    rs485_thread_create("rs485",1048,10,10);
    
    return RT_EOK;
}
INIT_APP_EXPORT(vfd_create_thread);
