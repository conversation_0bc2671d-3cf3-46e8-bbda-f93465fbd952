
/***************************************************************************

Copyright (C), 2022-2022, BSM Tech. Co., Ltd.

* @file           pfc_isr.c
* <AUTHOR> @version        V0.0.1
* @date           2022-09-06
* @brief          this is PFC isr

History:          // Revision Records

       <Author>             <time>         <version >               <desc>



***************************************************************************/
#include "pfc_isr.h"
#include "pfc_pwm.h"
#include "pfc_dqctrl.h"
#include "pfc_sdpll.h"

#define LED_COMM_PIN    GET_PIN(G, 1)

static uint32_t ctrlFreqCnt = 0;
static volatile uint32_t enter_tick;
static volatile uint32_t leave_tick;
static volatile uint32_t use_tick;
static void ad_sample_filter(void);
extern void sDPLL3P(void);
extern void pfc_pwmctrl(void);
extern ADC_HandleTypeDef hadc4;
extern DMA_HandleTypeDef hdma_adc4;
extern void adc_pfc_dma_isr(void);
extern void PfcDisPwm(void);
extern void tim8_break_isr(void);
volatile int pfc_irqing_flag = 0;
static void fast_cache_data(void);
extern uint16_t pfc_adc_value[8];

CCMRAM void adc1_dma_isr(void)
{
    static uint8_t freq_cnt = 0;
    if(!vfd.bit.system_init)    return;
    
    pfc_irqing_flag = 1;

    enter_tick = TIM3->CNT;
    pfc.cycle_tick = (enter_tick > pfc.prev_tick) ? (enter_tick - pfc.prev_tick) : pfc.cycle_tick;
    pfc.prev_tick  = enter_tick;
    
    if(vfd.is_update_fw)
    {
        vfd.inverter->ops->stop();
        vfd.pfc->ops->stop();
        return;
    }
    
    
    ad_sample_filter();
    
    adc_pfc_dma_isr(); /* pfc main int function */
    
    float abs_freq = fabs(DPLL3P.DPLLDDSRF.lfo);
    
    if(((int)abs_freq >= 40)
            &&  ((int)abs_freq <= 65))
    {
        freq_cnt = 0;
        vfd.bit.isr_freq_ready = 1;
    }
    else if( (!vfd.ctrl.start_pfc_invmode)
         &&  ((abs_freq <= 5) || (abs_freq >= 95)
              ||  ((fabs(DPLL3P.VLac.In.a) <= 50) && (fabs(DPLL3P.VLac.In.b) <= 50) && (fabs(DPLL3P.VLac.In.c) <= 50)))
    )
    {
        if(freq_cnt < 20)
            freq_cnt++;
        else
        {
            vfd.bit.isr_freq_ready = 0;
            if(PFC_IS_RUN)
                vfd.bit.freq_err_flag = 1;
            #ifndef VFD_TEST_DEBUG
            if((ST_RUN == vfd.ctrl.sys_st) && PFC_IS_RUN)
                vfd.inverter->ops->stop();
            #endif
            
            vfd.pfc->ops->stop();
        }
    }
        
    static uint8_t flag_save = 0;
    
    if(flag_save != vfd.bit.isr_freq_ready)
        vfd.freq_switch_cnt++;
    
    flag_save = vfd.bit.isr_freq_ready;
    vfd.freq_err_cnt = freq_cnt;
    
    fast_cache_data();
    
    
    //cal cpu usage
    leave_tick = TIM3->CNT;
    
    if(leave_tick > enter_tick)
        use_tick = (leave_tick - enter_tick);
    else
        use_tick = (leave_tick + 0xFFFF - enter_tick);
    
    pfc.cpu = use_tick*(float)Tim8_Freq/1000000.0f;
    pfc.use_tick = use_tick;
    
    if(pfc.cpu > 1)
       pfc.cpu_overload++;
        
    pfc_irqing_flag = 0;

}

#include "alg_pcmaster.h"
/**
  * @brief  ADC4 
  * @param 
  * @retval 
  */
void DMA2_Channel2_IRQHandler(void)
{
    
    rt_ubase_t  level;

    /* USER CODE BEGIN DMA1_Channel1_IRQn 0 */
    HAL_DMA_IRQHandler(&hdma_adc4);
    level = rt_hw_interrupt_disable();

    adc1_dma_isr();
    rt_hw_interrupt_enable(level);
    
    
    /* USER CODE END DMA1_Channel1_IRQn 0 */
}


/*=======================================================*/
/**
 * @brief   Calibrate AD offset.
 * @return  None.
 */
 CurExcursion_t gUinExcursion;
 CurExcursion_t gIinExcursion;
CCMRAM void pfc_3PCalibADOffset(void)
{
    if(!gIinExcursion.EnableFlag)
    {
        gIinExcursion.ZeroIu = IA_OFFSET_A;
        gIinExcursion.ZeroIv = IB_OFFSET_A;
        gIinExcursion.ZeroIw = IC_OFFSET_A;
    }
    
    if((vfd.pfc->ControlState != ST_STOP) || (vfd.io.kmon3)) 
    {
        gIinExcursion.EnableCount = 0;
        return;
    }
   
	gIinExcursion.Iu  = ((float)pfc_adc_value[4] * USER_CURRENT_SF) ;
	gIinExcursion.Iv  = ((float)pfc_adc_value[5] * USER_CURRENT_SF) ;
    gIinExcursion.Iw  = ((float)pfc_adc_value[6] * USER_CURRENT_SF) ;
    
    gIinExcursion.EnableCount++;
    gIinExcursion.EnableCount = ( gIinExcursion.EnableCount > 200) ? 200 : gIinExcursion.EnableCount;
    if(gIinExcursion.EnableCount < 200)
    {
        gIinExcursion.TotalIu = 0;
        gIinExcursion.TotalIv = 0;
        gIinExcursion.TotalIw = 0;
        gIinExcursion.Count = 0;
        return;
    }
    
    gIinExcursion.TotalIu += gIinExcursion.Iu;
    gIinExcursion.TotalIv += gIinExcursion.Iv;
    gIinExcursion.TotalIw += gIinExcursion.Iw;
    gIinExcursion.Count++;
    
    if(gIinExcursion.Count >= 2000)
    {
        float m_ZeroIu,m_ZeroIv,m_ZeroIw;
        
        m_ZeroIu = gIinExcursion.TotalIu * 0.0005f;
        m_ZeroIv = gIinExcursion.TotalIv * 0.0005f;
        m_ZeroIw = gIinExcursion.TotalIw * 0.0005f;
        
        gIinExcursion.TotalIu = 0;
        gIinExcursion.TotalIv = 0;
        gIinExcursion.TotalIw = 0;
        gIinExcursion.Count = 0;
        
        if((fabs(m_ZeroIu-IA_OFFSET_A) <= 1.5f) &&
           (fabs(m_ZeroIv-IB_OFFSET_A) <= 1.5f) &&
           (fabs(m_ZeroIw-IC_OFFSET_A) <= 1.5f) )
        {
            gIinExcursion.ZeroIu = m_ZeroIu;
            gIinExcursion.ZeroIv = m_ZeroIv;
            gIinExcursion.ZeroIw = m_ZeroIw;
            gIinExcursion.ErrCnt = 0;
            gIinExcursion.EnableFlag = 1;
        }
        else if(gIinExcursion.ErrCnt++ >= 5)
        {
            gIinExcursion.ErrCnt = 0;
            gIinExcursion.EnableCount = 0;
        }
        
    }
    
}


/**
  * @brief 
  * @param 
  * @retval 
  */
void adc_pfc_dma_isr(void)
{
    ctrlFreqCnt ++;
    
    {
        //LED_COM_OUT(1);
        
        sDPLL3P();
        
        #ifdef VFD_TEST_DEBUG
        if( !vfd.ctrl.start_pfc     &&
            (vfd.manual.tim8_u_duty || vfd.manual.tim8_v_duty || vfd.manual.tim8_w_duty)
        )
        {
            
        }
        else 
        #endif
        {
            pfc_qdctrl();
            pfc_pwmctrl();
        }
        
        
        pfc_3PCalibADOffset();   /* only use to get cali data */
        //pfc_3PUinCalibADOffset();
        
        
        pfc.PFC_PreRuning = pfc.PFC_Runing;
      
        //LED_COM_OUT(0);
    }
    
}

/**
  * @brief 
  * @param 
  * @retval 
  */
void TIM8_BRK_IRQHandler(void)
{
  /* USER CODE BEGIN TIM1_BRK_TIM15_IRQHandler 0 */
  if (LL_TIM_IsActiveFlag_BRK(TIM8))
  {
      LL_TIM_ClearFlag_BRK(TIM8);
      if( FHD_ERROR)
        tim8_break_isr();
  }
  
  if (LL_TIM_IsActiveFlag_BRK2(TIM8))
  {
      LL_TIM_ClearFlag_BRK2(TIM8);
      if( FHD_ERROR)
        tim8_break_isr();
  }
  /* USER CODE END TIM1_BRK_TIM15_IRQHandler 0 */
}

#include "app_ad.h"
extern uint16_t pfc_adc_value[8];

// Test function: verify the precision of shift division
// Can be called during debugging to verify precision
static void test_div3_precision(void)
{
    // Test different input values
    uint32_t test_values[] = {0, 1, 2, 3, 100, 1000, 4095, 8190, 12285};
    int test_count = sizeof(test_values) / sizeof(test_values[0]);

    for(int i = 0; i < test_count; i++)
    {
        uint32_t input = test_values[i];
        uint32_t shift_result = (input * 0x5555U) >> 16;
        uint32_t div_result = input / 3;
        int32_t error = (int32_t)shift_result - (int32_t)div_result;

        // These values can be observed in the debugger
        // printf("Input: %u, Shift: %u, Div: %u, Error: %d\n",
        //        input, shift_result, div_result, error);
    }
}
// Sampling filter function: use shift operations instead of division by 3 to improve speed
static void ad_sample_filter(void)
{
    float vbus_cali = 0;

    // Use shift operations instead of division by 3 to improve speed
    // Method: (x * 0x5555) >> 16 ≈ x / 3
    // Precision analysis:
    // - 0x5555 = 21845, theoretical value 65536/3 ≈ 21845.33
    // - Maximum relative error: about 0.0015% (for 16-bit ADC values)
    // - For ADC value range 0-4095, maximum absolute error about ±0.06
    // - For sum of three values (max 12285), maximum absolute error about ±0.18
    pfc_adc_value[0] = ((uint32_t)(ADC4_ValTemp[0]+ADC4_ValTemp[3]+ADC4_ValTemp[6]) * 0x5555U) >> 16;
    pfc_adc_value[1] = ((uint32_t)(ADC4_ValTemp[1]+ADC4_ValTemp[4]+ADC4_ValTemp[7]) * 0x5555U) >> 16;
    pfc_adc_value[2] = ((uint32_t)(ADC4_ValTemp[2]+ADC4_ValTemp[5]+ADC4_ValTemp[8]) * 0x5555U) >> 16;

    uint16_t vbus_ad = ADC4_ValTemp[9];
    vbus_cali = 0;

    pfc_adc_value[3] = (vbus_ad + vbus_cali);

    pfc_adc_value[4] = ((uint32_t)(ADC5_ValTemp[0]+ADC5_ValTemp[3]+ADC5_ValTemp[6]) * 0x5555U) >> 16;
    pfc_adc_value[5] = ((uint32_t)(ADC5_ValTemp[1]+ADC5_ValTemp[4]+ADC5_ValTemp[7]) * 0x5555U) >> 16;
    pfc_adc_value[6] = ((uint32_t)(ADC5_ValTemp[2]+ADC5_ValTemp[5]+ADC5_ValTemp[8]) * 0x5555U) >> 16;
    
    pfc_adc_value[7] = vbus_ad;

}

static void fast_cache_data(void)
{
    static uint32_t cnt = 0;
    cnt++;
    
    if(vfd.disp_ctrl == 0) return;
    
    #ifdef DEBUG_SERIAL_PLOT
    
    if(cnt % 2  == 0) 
    {
        if((vfd.debug_picnt < DEBUG_PLOT_POINTS)
        )
        {
            if(vfd.debug_data_index == 10)
            {      
                vfd.debug_data1[vfd.debug_picnt] = DPLL3P.VLac.In.a;
                vfd.debug_data2[vfd.debug_picnt] = DPLL3P.VLac.In.b;            
                vfd.debug_data3[vfd.debug_picnt] = DPLL3P.VLac.In.c;
//                vfd.debug_data4[vfd.debug_picnt] = DPLL3P.Vbus.In;
           
            }
            else if(vfd.debug_data_index == 11)
            {
//                if(pfc.PFC_Runing == 0)
//                {
//                    if(vfd.debug_picnt > 0)
//                        vfd.debug_picnt = DEBUG_PLOT_POINTS;
//                    return;
//                }
                
                vfd.debug_data1[vfd.debug_picnt] = DPLL3P.Iac.In.a;
                vfd.debug_data2[vfd.debug_picnt] = DPLL3P.IabIdq.d;
                vfd.debug_data3[vfd.debug_picnt] = DPLL3P.IabIdq.q;
         
            }
            else if(vfd.debug_data_index == 12)
            {
                if(pfc.PFC_Runing == 0)
                {
                    if(vfd.debug_picnt > 0)
                        vfd.debug_picnt = DEBUG_PLOT_POINTS;
                    
                    return;
                }
                
                vfd.debug_data1[vfd.debug_picnt] = DPLL3P.Iac.In.a;
                vfd.debug_data2[vfd.debug_picnt] = PFCVAR.PIId.Out;
                vfd.debug_data3[vfd.debug_picnt] = PFCVAR.L;
          
            }
            else if(vfd.debug_data_index == 13)
            {
//                if(pfc.PFC_Runing == 0)
//                {
//                    if(vfd.debug_picnt > 0)
//                        vfd.debug_picnt = DEBUG_PLOT_POINTS;
//                    return;
//                }
                
                vfd.debug_data1[vfd.debug_picnt] = DPLL3P.Iac.In.a;
                vfd.debug_data2[vfd.debug_picnt] = PFCVAR.Vout.d;            
                vfd.debug_data3[vfd.debug_picnt] = PFCVAR.Vout.q;
              
              
            }
            else if(vfd.debug_data_index == 14)
            {
                vfd.debug_data1[vfd.debug_picnt] = DPLL3P.DPLLDDSRF.dpdelpf;
                vfd.debug_data2[vfd.debug_picnt] = DPLL3P.Vbus.In;
                vfd.debug_data3[vfd.debug_picnt] = DPLL3P.DPLLDDSRF.qpdelpf;
  
               
            }
//            else if(vfd.debug_data_index == 15)
//            {
//                if(pfc.PFC_Runing == 0)
//                {
//                    if(vfd.debug_picnt > 0)
//                        vfd.debug_picnt = DEBUG_PLOT_POINTS;
//                    return;
//                }
//                vfd.debug_data1[vfd.debug_picnt] = PFCVAR.Vout.alpha;
//                vfd.debug_data2[vfd.debug_picnt] = DPLL3P.Vac.In.a;
//                vfd.debug_data3[vfd.debug_picnt] = DPLL3P.DPLLDDSRF.theta;
//                vfd.debug_data4[vfd.debug_picnt] = DPLL3P.Iac.In.a;
//            }
//            else if(vfd.debug_data_index == 16)
//            {
//                vfd.debug_data1[vfd.debug_picnt] = rt_pin_read(FS_DR_PIN)   ? 100 : 0;
//                vfd.debug_data2[vfd.debug_picnt] = rt_pin_read(FS_DR_B_PIN) ? 100 : 0;
//                vfd.debug_data3[vfd.debug_picnt] = PFCVAR.PIVbus.Ref;
//                vfd.debug_data4[vfd.debug_picnt] = DPLL3P.Vbus.In;
//         
//            }
//            else if(vfd.debug_data_index == 17)
//            {
//                vfd.debug_data1[vfd.debug_picnt] = PFCVAR.Vout.alpha;
//                vfd.debug_data2[vfd.debug_picnt] = -DPLL3P.Iac.In.a;
//                vfd.debug_data3[vfd.debug_picnt] = -DPLL3P.Iac.In.b;
//                vfd.debug_data4[vfd.debug_picnt] = -DPLL3P.Iac.In.c;
//             
//            }
//            else if(vfd.debug_data_index == 18)
//            {
//                vfd.debug_data1[vfd.debug_picnt] = DPLL3P.IabIdq.d;
//                vfd.debug_data2[vfd.debug_picnt] = DPLL3P.IabIdq.q;
//                vfd.debug_data3[vfd.debug_picnt] = PFCVAR.PIId.Out;
//                vfd.debug_data4[vfd.debug_picnt] = PFCVAR.PIIq.Out;
//        
//            }
//            else if(vfd.debug_data_index == 19)
//            {
//                vfd.debug_data1[vfd.debug_picnt] = pfc_adc_value[0];
//                vfd.debug_data2[vfd.debug_picnt] = pfc_adc_value[1];
//                vfd.debug_data3[vfd.debug_picnt] = pfc_adc_value[2];
//                vfd.debug_data4[vfd.debug_picnt] = pfc_adc_value[3];
//            }
//            else if(vfd.debug_data_index == 20)
//            {
//                vfd.debug_data1[vfd.debug_picnt] = PFCVAR.SVPWM.CntPhA;
//                vfd.debug_data2[vfd.debug_picnt] = PFCVAR.SVPWM.CntPhB;
//                vfd.debug_data3[vfd.debug_picnt] = PFCVAR.SVPWM.CntPhC;
//                vfd.debug_data4[vfd.debug_picnt] = PFCVAR.SVPWM.CntPhC;
//            }
            else
                return;
            
            vfd.debug_picnt++;  
        }
    }
    #endif
}
/*******************************************************************************
* Function Name  : void tim8_break_isr(void)
* Description    : over cur, close pwm
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void tim8_break_isr(void)
{
    static uint32_t tick = 0;
    /* over current .*/
    LL_TIM_DisableIT_BRK(TIM8);

    hardware_irq_pin.f_ipm_pfc_cnt++;
    hardware_irq_pin.f_tim8_breakin_flag = RT_TRUE;
    dio_table[dio_pin_find(F_IPM_PFC_PIN)].irq_delay = IRQ_DELAY_DEFAULT;
    
    #ifndef DEBUG_PFC_DISABLE_SOFT_PROTECT
    if(vfd.ctrl.start_pfc && FHD_ERROR)
    {
        pfc_fault_stop();
    }
    else if(vfd.ctrl.start_dcdc)
    {

    }
    #endif
    
    tick = rt_tick_get();
}

